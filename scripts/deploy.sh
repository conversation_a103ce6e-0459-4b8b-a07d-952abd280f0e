#!/bin/bash

# 数峪科技官网部署脚本

set -e

echo "🚀 开始部署数峪科技官网..."

# 检查环境
echo "📋 检查部署环境..."
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装"
    exit 1
fi

if ! command -v npm &> /dev/null; then
    echo "❌ npm 未安装"
    exit 1
fi

# 安装依赖
echo "📦 安装项目依赖..."
npm ci

# 运行类型检查
echo "🔍 运行类型检查..."
npm run type-check

# 运行代码检查
echo "🔍 运行代码检查..."
npm run lint

# 构建项目
echo "🏗️ 构建项目..."
npm run build

# 检查构建结果
if [ ! -d "out" ]; then
    echo "❌ 构建失败，out 目录不存在"
    exit 1
fi

echo "✅ 构建完成"

# 部署到 CloudBase
if command -v tcb &> /dev/null; then
    echo "🌐 部署到 CloudBase..."
    tcb framework deploy
    echo "✅ 部署完成"
else
    echo "⚠️ CloudBase CLI 未安装，跳过自动部署"
    echo "📁 静态文件已生成在 out 目录中"
fi

echo "🎉 部署流程完成！"
