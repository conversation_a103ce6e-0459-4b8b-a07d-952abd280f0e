# 数峪科技官方网站

> 用心开发，创造实用产品

这是数峪科技 (ShuyuTech) 的官方网站，采用现代化的技术栈构建。我们是一个由两名在读大学生组成的初创团队，成立于2025年7月，专注于开发简洁实用的Web和移动应用。

## 🚀 技术栈

- **框架**: Next.js 14 (App Router)
- **样式**: Tailwind CSS
- **语言**: TypeScript
- **图标**: Lucide React
- **部署**: CloudBase (腾讯云)
- **内容**: Markdown 驱动的博客系统

## 📱 功能特性

### 核心页面
- ✅ **首页** - Hero 区、产品展示、团队优势、博客精选
- ✅ **关于我们** - 公司愿景、团队介绍、发展历程
- ✅ **我们的产品** - 产品展示和详细介绍
- ✅ **技术博客** - Markdown 驱动的博客系统

### 联系方式
- 📧 **邮箱联系**: <EMAIL>
- 📍 **公司地址**: 北京市朝阳区科技园区创新大厦 A 座 1001 室
- ⚠️ **注意**: 为了更好地匹配初创团队规模，我们暂时不提供电话联系服务，请通过邮箱与我们联系

### 设计特性
- 🎨 **现代化设计** - 简洁、活泼、亲和力强
- 📱 **完全响应式** - 移动端优先设计
- 🌈 **天蓝色主题** - 体现初创企业的活力与友好
- ✨ **几何装饰** - 简单几何图形和磨砂玻璃特效
- ⚡ **性能优化** - 静态生成，快速加载
- 🔍 **SEO 友好** - 完整的元数据和结构化数据

## 🛠️ 开发指南

### 环境要求
- Node.js 18+
- npm 或 yarn

### 安装依赖
```bash
npm install
```

### 开发服务器
```bash
npm run dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看网站。

### 构建项目
```bash
npm run build
```

### 其他命令
```bash
# 类型检查
npm run type-check

# 代码检查
npm run lint

# 修复代码问题
npm run lint:fix

# 清理构建文件
npm run clean
```

## 📁 项目结构

```
src/
├── app/                    # Next.js App Router 页面
│   ├── about/             # 关于我们页面
│   ├── blog/              # 博客页面
│   ├── contact/           # 联系我们页面
│   ├── portfolio/         # 产品案例页面
│   ├── services/          # 服务项目页面
│   ├── globals.css        # 全局样式
│   ├── layout.tsx         # 根布局
│   ├── page.tsx           # 首页
│   ├── sitemap.ts         # 站点地图
│   ├── robots.ts          # 爬虫规则
│   └── manifest.ts        # PWA 配置
├── components/            # React 组件
│   ├── layout/           # 布局组件
│   ├── sections/         # 页面区块组件
│   └── ui/               # 基础 UI 组件
├── content/              # 内容文件
│   └── blog/            # 博客 Markdown 文件
└── lib/                  # 工具函数
    ├── blog.ts          # 博客相关函数
    ├── seo.ts           # SEO 工具
    └── utils.ts         # 通用工具
```

## 🎨 设计系统

> **重大设计重构**: 为了更好地体现初创企业的活力和亲和力，我们进行了全面的设计重构：从深蓝色企业风格转向个人网站风格，采用全局背景系统、磨砂玻璃美学和SVG图标系统，营造更加友好和创造性的视觉体验。

### 颜色方案
- **主色调**: 天蓝色系 (#0ea5e9, #38bdf8) - 友好亲和
- **强调色**: 活力橙色 (#f97316, #fb923c) - 增加活力
- **辅助色**: 温暖粉色 (#ec4899, #f472b6) - 个性化点缀
- **背景**: 全局固定渐变 (blue-50 → cyan-50 → orange-50) - 明亮清新

### 设计特性
- **全局背景系统**: 固定的装饰性背景，不随页面滚动
- **磨砂玻璃美学**: 透明度和模糊效果创建现代视觉层次
- **SVG图标系统**: 替代emoji，保持视觉一致性
- **个人化风格**: 去除企业正式感，营造个人网站亲和力

### 字体
- **主字体**: Inter (Google Fonts)
- **代码字体**: JetBrains Mono

### 响应式断点
- **移动端**: < 768px
- **平板端**: 768px - 1024px
- **桌面端**: > 1024px

## 📝 内容管理

### 博客文章
博客文章使用 Markdown 格式，存放在 `src/content/blog/` 目录中。

文章格式示例：
```markdown
---
title: "文章标题"
date: "2024-01-15"
excerpt: "文章摘要"
author: "作者姓名"
category: "分类"
tags: ["标签1", "标签2"]
readTime: "8 分钟"
featured: true
---

# 文章内容

这里是文章的正文内容...
```

## 🚀 部署指南

### CloudBase 部署

1. **安装 CloudBase CLI**
```bash
npm install -g @cloudbase/cli
```

2. **登录 CloudBase**
```bash
tcb login
```

3. **配置环境**
复制 `.env.example` 为 `.env.local` 并填入配置：
```bash
cp .env.example .env.local
```

4. **部署**
```bash
npm run deploy
```

### 手动部署

1. **构建项目**
```bash
npm run build
```

2. **上传文件**
将 `out/` 目录中的所有文件上传到您的静态网站托管服务。

## 🔧 配置说明

### 环境变量
参考 `.env.example` 文件配置以下环境变量：
- `NEXT_PUBLIC_SITE_URL` - 网站 URL
- `NEXT_PUBLIC_CONTACT_EMAIL` - 联系邮箱
- `NEXT_PUBLIC_GA_ID` - Google Analytics ID

### CloudBase 配置
编辑 `cloudbaserc.json` 文件：
```json
{
  "envId": "your-env-id",
  "region": "ap-shanghai"
}
```

## 📊 性能指标

### 构建结果
- **首页**: ~96KB (First Load JS)
- **内页**: ~87-107KB
- **静态资源**: 高效压缩和缓存

### 目标指标
- **LCP**: < 2.5s
- **FID**: < 100ms
- **CLS**: < 0.1
- **Lighthouse 评分**: 90+

## 📝 最近更新 (2025年1月)

我们根据团队的实际情况对网站进行了重大设计改版：

### 设计系统重构
- 🎨 **视觉风格**: 从深蓝色企业风格改为天蓝色个人化风格
- 🏗️ **架构重构**: 实现全局背景系统，各Section使用透明磨砂玻璃效果
- 🎯 **个人化定位**: 强调"两个大学生"的身份，去除企业正式感
- 📱 **组件系统**: 新增GlassCard、CustomIcons等现代化组件
- ✨ **动画效果**: 添加浮动几何装饰和优雅的交互动画

### 技术优化
- ⚡ **性能提升**: 优化编译时间，删除不必要的依赖包
- 🔧 **代码重构**: 统一组件架构，提高代码可维护性
- 📱 **响应式优化**: 完善移动端适配和用户体验

### 当前产品
- **FindU**: 简洁实用的在线工具 (https://findu.shuyutech.online)

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系我们

- **网站**: https://shuyutech.com
- **邮箱**: <EMAIL>
- **注意**: 我们暂时不提供电话联系服务，请通过邮箱与我们联系

---

© 2025 数峪科技 (ShuyuTech). 保留所有权利。
