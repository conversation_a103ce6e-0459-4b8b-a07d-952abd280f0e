/** @type {import('next').NextConfig} */
const nextConfig = {
  output: 'export',
  trailingSlash: true,
  images: {
    unoptimized: true,
    formats: ['image/webp', 'image/avif']
  },
  compress: true,
  poweredByHeader: false,
  generateEtags: false,

  // 性能优化
  swcMinify: true,

  // 环境变量
  env: {
    CUSTOM_KEY: process.env.CUSTOM_KEY,
  },

  // 注意：静态导出模式下，redirects 和 headers 需要在服务器层面配置

  // 静态页面配置
  generateBuildId: async () => {
    return 'shuyu-tech-' + Date.now()
  },
}

module.exports = nextConfig
