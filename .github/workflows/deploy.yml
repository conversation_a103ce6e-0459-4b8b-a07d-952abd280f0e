# 工作流名称
name: Deploy Official Site to Server

# 触发条件：当有代码 push 到 main 分支时
on:
  push:
    branches:
      - main

# 工作任务
jobs:
  deploy:
    # 运行环境：使用最新的Ubuntu系统
    runs-on: ubuntu-latest

    # 步骤
    steps:
      # 第1步：检出代码
      - name: Checkout code
        uses: actions/checkout@v4

      # 第2步：设置Node.js环境
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18' # 或者您需要的Node.js版本
          cache: 'npm' # 缓存npm依赖，加快后续构建速度

      # 第3步：安装项目依赖
      - name: Install dependencies
        run: npm install

      # 第4步：构建项目
      - name: Build project
        run: npm run build # 这会生成 out 目录

      # 第5步：部署到服务器
      - name: Deploy to Server
        uses: appleboy/scp-action@master
        with:
          host: ${{ secrets.SERVER_HOST }} # 从Secrets读取服务器IP
          username: ${{ secrets.SERVER_USER }} # 从Secrets读取用户名
          key: ${{ secrets.SSH_PRIVATE_KEY }} # 从Secrets读取私钥
          source: "out/*" # 要传输的源文件，out/目录下所有内容
          target: "~/official-site/html" # 服务器上的目标路径
          strip_components: 1 # 去掉源路径的第一层目录(out)，只传输其内容
          overwrite: true # 覆盖目标文件
