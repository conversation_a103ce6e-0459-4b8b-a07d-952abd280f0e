/**
 * 透明Section组件
 * 替代原有的Section组件，使用透明背景与全局背景协调
 * 提供统一的间距和布局
 */

import React from 'react'
import { cn } from '@/lib/utils'

interface TransparentSectionProps {
  children: React.ReactNode
  className?: string
  padding?: 'sm' | 'md' | 'lg' | 'xl'
  container?: boolean
  id?: string
}

const TransparentSection: React.FC<TransparentSectionProps> = ({
  children,
  className,
  padding = 'lg',
  container = true,
  id
}) => {
  const paddingClasses = {
    sm: 'py-8 md:py-12',
    md: 'py-12 md:py-16',
    lg: 'py-16 md:py-24',
    xl: 'py-24 md:py-32'
  }
  
  const containerClasses = container ? 'container-custom' : ''
  
  return (
    <section
      id={id}
      className={cn(
        'relative',
        paddingClasses[padding],
        className
      )}
    >
      <div className={containerClasses}>
        {children}
      </div>
    </section>
  )
}

export default TransparentSection
