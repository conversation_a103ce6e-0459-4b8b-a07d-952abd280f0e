/**
 * 磨砂玻璃效果卡片组件
 * 提供统一的卡片样式，支持不同的变体和尺寸
 * 使用磨砂玻璃效果与全局背景协调
 */

import React from 'react'
import { cn } from '@/lib/utils'

interface GlassCardProps {
  children: React.ReactNode
  className?: string
  variant?: 'default' | 'primary' | 'accent' | 'secondary'
  size?: 'sm' | 'md' | 'lg'
  hover?: boolean
  onClick?: () => void
}

const GlassCard: React.FC<GlassCardProps> = ({
  children,
  className,
  variant = 'default',
  size = 'md',
  hover = true,
  onClick
}) => {
  const baseClasses = 'backdrop-blur-md border rounded-xl transition-all duration-300'
  
  const variantClasses = {
    default: 'bg-white/20 border-white/30 hover:bg-white/30',
    primary: 'bg-primary-100/30 border-primary-200/50 hover:bg-primary-100/40',
    accent: 'bg-accent-100/30 border-accent-200/50 hover:bg-accent-100/40',
    secondary: 'bg-secondary-100/30 border-secondary-200/50 hover:bg-secondary-100/40'
  }
  
  const sizeClasses = {
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8'
  }
  
  const hoverClasses = hover ? 'hover:shadow-lg hover:shadow-black/10 hover:scale-[1.02]' : ''
  
  return (
    <div
      className={cn(
        baseClasses,
        variantClasses[variant],
        sizeClasses[size],
        hoverClasses,
        onClick && 'cursor-pointer',
        className
      )}
      onClick={onClick}
    >
      {children}
    </div>
  )
}

export default GlassCard
