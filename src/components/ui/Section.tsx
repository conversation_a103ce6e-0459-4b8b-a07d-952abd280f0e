import React from 'react'
import { cn } from '@/lib/utils'
import Container from './Container'

interface SectionProps {
  children: React.ReactNode
  className?: string
  containerClassName?: string
  id?: string
  background?: 'default' | 'gradient' | 'pattern'
  padding?: 'sm' | 'md' | 'lg' | 'xl'
}

const Section: React.FC<SectionProps> = ({
  children,
  className,
  containerClassName,
  id,
  background = 'default',
  padding = 'lg'
}) => {
  const backgrounds = {
    default: '',
    gradient: 'bg-gradient-to-b from-dark-950 via-dark-900 to-dark-950',
    pattern: 'relative overflow-hidden'
  }

  const paddings = {
    sm: 'py-12',
    md: 'py-16',
    lg: 'py-20 md:py-24',
    xl: 'py-24 md:py-32'
  }

  return (
    <section
      id={id}
      className={cn(
        backgrounds[background],
        paddings[padding],
        className
      )}
    >
      {background === 'pattern' && (
        <div className="absolute inset-0 opacity-5">
          <div className="absolute inset-0 bg-[url('/images/grid-pattern.svg')] bg-center [mask-image:linear-gradient(180deg,white,rgba(255,255,255,0))]" />
        </div>
      )}
      <Container className={containerClassName}>
        {children}
      </Container>
    </section>
  )
}

export default Section
