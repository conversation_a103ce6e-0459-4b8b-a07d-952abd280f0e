'use client'

import React from 'react'

const ResponsiveIndicator: React.FC = () => {
  if (process.env.NODE_ENV !== 'development') {
    return null
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 bg-dark-900 border border-accent-500 rounded-lg px-3 py-2 text-xs font-mono text-accent-400">
      <div className="sm:hidden">XS (&lt;640px)</div>
      <div className="hidden sm:block md:hidden">SM (640px+)</div>
      <div className="hidden md:block lg:hidden">MD (768px+)</div>
      <div className="hidden lg:block xl:hidden">LG (1024px+)</div>
      <div className="hidden xl:block 2xl:hidden">XL (1280px+)</div>
      <div className="hidden 2xl:block">2XL (1536px+)</div>
    </div>
  )
}

export default ResponsiveIndicator
