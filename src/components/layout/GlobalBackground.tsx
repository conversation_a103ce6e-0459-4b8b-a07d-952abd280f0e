/**
 * 全局背景组件
 * 提供固定的背景效果，不随页面滚动而移动
 * 包含渐变背景、装饰性几何图形和动画效果
 */

'use client'

import React from 'react'

const GlobalBackground: React.FC = () => {
  return (
    <div className="fixed inset-0 -z-10 overflow-hidden">
      {/* 主背景渐变 */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-cyan-50 to-orange-50" />
      
      {/* 次级渐变层 */}
      <div className="absolute inset-0 bg-gradient-to-tr from-primary-100/30 via-transparent to-accent-100/20" />
      
      {/* 装饰性几何图形 - 大型背景元素 */}
      <div className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-primary-200/20 to-primary-300/10 rounded-full blur-3xl animate-float" />
      <div className="absolute top-1/4 right-0 w-80 h-80 bg-gradient-to-bl from-accent-200/20 to-accent-300/10 rounded-full blur-3xl animate-float" style={{ animationDelay: '2s' }} />
      <div className="absolute bottom-0 left-1/4 w-72 h-72 bg-gradient-to-tr from-secondary-200/20 to-secondary-300/10 rounded-full blur-3xl animate-float" style={{ animationDelay: '4s' }} />
      <div className="absolute bottom-1/4 right-1/3 w-64 h-64 bg-gradient-to-tl from-primary-200/15 to-accent-200/15 rounded-full blur-3xl animate-float" style={{ animationDelay: '6s' }} />
      
      {/* 中型装饰元素 */}
      <div className="absolute top-1/3 left-1/2 w-48 h-48 bg-gradient-to-r from-accent-300/15 to-secondary-300/15 rounded-full blur-2xl animate-float" style={{ animationDelay: '1s' }} />
      <div className="absolute top-2/3 left-1/6 w-40 h-40 bg-gradient-to-l from-primary-300/15 to-accent-300/15 rounded-full blur-2xl animate-float" style={{ animationDelay: '3s' }} />
      <div className="absolute top-1/6 right-1/4 w-36 h-36 bg-gradient-to-b from-secondary-300/15 to-primary-300/15 rounded-full blur-2xl animate-float" style={{ animationDelay: '5s' }} />
      
      {/* 小型点缀元素 */}
      <div className="absolute top-1/5 left-3/4 w-24 h-24 bg-gradient-to-br from-accent-400/20 to-primary-400/20 rounded-full blur-xl animate-float" style={{ animationDelay: '0.5s' }} />
      <div className="absolute top-3/5 right-1/6 w-20 h-20 bg-gradient-to-bl from-secondary-400/20 to-accent-400/20 rounded-full blur-xl animate-float" style={{ animationDelay: '2.5s' }} />
      <div className="absolute bottom-1/5 left-1/3 w-28 h-28 bg-gradient-to-tr from-primary-400/20 to-secondary-400/20 rounded-full blur-xl animate-float" style={{ animationDelay: '4.5s' }} />
      
      {/* 几何形状装饰 */}
      <div className="absolute top-1/4 left-1/5 w-16 h-16 bg-primary-300/10 rotate-45 animate-float" style={{ animationDelay: '1.5s' }} />
      <div className="absolute top-3/4 right-1/5 w-12 h-12 bg-accent-300/10 rounded-lg rotate-12 animate-float" style={{ animationDelay: '3.5s' }} />
      <div className="absolute bottom-1/3 left-2/3 w-14 h-14 bg-secondary-300/10 rounded-full animate-float" style={{ animationDelay: '5.5s' }} />
      
      {/* 网格图案 - 可选 */}
      <div className="absolute inset-0 bg-[url('/images/grid-pattern.svg')] bg-center opacity-5" />
      
      {/* 顶部渐变遮罩 - 为导航栏提供更好的对比度 */}
      <div className="absolute top-0 left-0 right-0 h-32 bg-gradient-to-b from-white/20 to-transparent" />
    </div>
  )
}

export default GlobalBackground
