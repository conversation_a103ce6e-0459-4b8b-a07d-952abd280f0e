import React from 'react'
import Link from 'next/link'
import { Mail, MapPin, Github, Twitter, Linkedin, Home, User, Monitor, FileText, Palette, Zap, Lightbulb, Rocket } from 'lucide-react'
import { NavigationIcon, MailIcon, LocationIcon, StarIcon } from '@/components/icons/CustomIcons'

const Footer: React.FC = () => {
  const currentYear = new Date().getFullYear()

  const quickLinks = [
    { name: '关于我们', href: '/about', icon: User },
    { name: '我们的作品', href: '/products', icon: Monitor },
    { name: '技术分享', href: '/blog', icon: FileText },
  ]

  const interests = [
    { name: '前端开发', href: '/blog', icon: Palette },
    { name: '性能优化', href: '/blog', icon: Zap },
    { name: '新技术探索', href: '/blog', icon: Rocket },
    { name: '创意实现', href: '/blog', icon: Lightbulb },
  ]

  const socialLinks = [
    { name: 'GitHub', href: '#', icon: Github },
    { name: 'Twitter', href: '#', icon: Twitter },
    { name: 'LinkedIn', href: '#', icon: Linkedin },
  ]

  return (
    <footer className="bg-gradient-to-br from-gray-50 to-blue-50 border-t border-gray-200">
      <div className="container-custom py-12 md:py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info - 个人化风格 */}
          <div className="lg:col-span-1">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-10 h-10 bg-gradient-to-br from-accent-400 to-secondary-400 rounded-xl flex items-center justify-center shadow-lg">
                <span className="text-white font-bold text-lg">数</span>
              </div>
              <div>
                <span className="text-lg font-bold text-gray-800">数峪科技</span>
                <span className="text-sm text-gray-500 ml-2">✨</span>
              </div>
            </div>
            <p className="text-gray-600 text-sm leading-relaxed mb-6">
              两个爱折腾的大学生，用代码创造有趣的东西<br/>
              我们相信简单的产品能解决复杂的问题
            </p>
            <div className="flex space-x-4">
              {socialLinks.map((social) => {
                const Icon = social.icon
                return (
                  <a
                    key={social.name}
                    href={social.href}
                    className="text-gray-500 hover:text-primary-600 transition-all duration-200 hover:scale-110 p-2 rounded-lg hover:bg-white/50"
                    aria-label={social.name}
                  >
                    <Icon size={20} />
                  </a>
                )
              })}
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-gray-800 font-semibold mb-4 flex items-center gap-2">
              <NavigationIcon size={18} />
              快速导航
            </h3>
            <ul className="space-y-2">
              {quickLinks.map((link) => {
                const Icon = link.icon
                return (
                  <li key={link.name}>
                    <Link
                      href={link.href}
                      className="text-gray-600 hover:text-primary-600 transition-colors duration-200 text-sm hover:underline flex items-center gap-2"
                    >
                      <Icon size={14} />
                      {link.name}
                    </Link>
                  </li>
                )
              })}
            </ul>
          </div>

          {/* Interests */}
          <div>
            <h3 className="text-gray-800 font-semibold mb-4 flex items-center gap-2">
              <StarIcon size={18} />
              我们的兴趣
            </h3>
            <ul className="space-y-2">
              {interests.map((interest) => {
                const Icon = interest.icon
                return (
                  <li key={interest.name}>
                    <Link
                      href={interest.href}
                      className="text-gray-600 hover:text-primary-600 transition-colors duration-200 text-sm hover:underline flex items-center gap-2"
                    >
                      <Icon size={14} />
                      {interest.name}
                    </Link>
                  </li>
                )
              })}
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h3 className="text-gray-800 font-semibold mb-4 flex items-center gap-2">
              <MailIcon size={18} />
              联系我们
            </h3>
            <div className="space-y-3">
              <div className="flex items-center space-x-3 p-3 bg-white/50 rounded-lg hover:bg-white/70 transition-colors">
                <Mail size={16} className="text-primary-500" />
                <a
                  href="mailto:<EMAIL>"
                  className="text-gray-600 hover:text-primary-600 transition-colors duration-200 text-sm"
                >
                  <EMAIL>
                </a>
              </div>
              <div className="flex items-start space-x-3 p-3 bg-white/50 rounded-lg">
                <LocationIcon size={16} className="text-primary-500 mt-0.5" />
                <span className="text-gray-600 text-sm">
                  北京市朝阳区科技园区<br />
                  创新大厦 A 座 1001 室
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Bar - 个人化风格 */}
        <div className="border-t border-gray-200 mt-12 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <p className="text-gray-500 text-sm">
              © {currentYear} 数峪科技 (ShuyuTech) - 用心做产品，用爱写代码
            </p>
            <div className="flex space-x-6">
              <Link
                href="/privacy"
                className="text-gray-500 hover:text-primary-600 transition-colors duration-200 text-sm hover:underline"
              >
                🔒 隐私政策
              </Link>
              <Link
                href="/terms"
                className="text-gray-500 hover:text-primary-600 transition-colors duration-200 text-sm hover:underline"
              >
                📋 服务条款
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer
