'use client'

import React from 'react'
import { Target, Eye, Heart } from 'lucide-react'
import GlassCard from '@/components/common/GlassCard'

const visionItems = [
  {
    icon: Target,
    title: '🎯 我们想做什么',
    description: '用代码解决生活中的小问题，让复杂的事情变简单。我们不追求高大上，只想做点实用的东西！'
  },
  {
    icon: Eye,
    title: '✨ 我们的小目标',
    description: '希望有一天，我们做的产品能被更多人用到，哪怕只是让一个人的生活变得更方便一点点。'
  },
  {
    icon: Heart,
    title: '💝 我们的坚持',
    description: '简单就是美，实用胜过炫酷。我们相信好的产品不需要说明书，用起来就是舒服！'
  }
]

const CompanyVision: React.FC = () => {
  return (
    <section className="relative py-24 md:py-32">
      <div className="container-custom">
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-800 mb-6">
            👋 嗨，我们是数峪科技
          </h1>
          <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
            两个还在上学的程序员，2025年开始一起折腾代码 💻<br/>
            我们喜欢用技术解决生活中遇到的小问题，也喜欢把想法变成现实。<br/>
            虽然团队很小，但我们对做产品这件事很认真！ ✨
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
          {visionItems.map((item, index) => {
            const Icon = item.icon
            return (
              <GlassCard key={item.title} className="text-center h-full" variant="default">
                <div className="p-6">
                  <div className="w-16 h-16 bg-gradient-to-br from-accent-500/20 to-primary-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Icon size={32} className="text-accent-400" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-800 mb-4">{item.title}</h3>
                  <p className="text-gray-600 leading-relaxed">
                    {item.description}
                  </p>
                </div>
              </GlassCard>
            )
          })}
        </div>

        {/* 公司介绍详情 */}
        <div className="max-w-4xl mx-auto">
          <GlassCard className="p-8" variant="primary">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div>
                <h2 className="text-3xl font-bold text-gray-800 mb-6">
                  小团队，大梦想
                </h2>
                <div className="space-y-4 text-gray-600">
                  <p>
                    我们是两个还在上学的程序员，虽然经验不如大厂的前辈们，
                    但我们有满满的热情和学习的动力！
                  </p>
                  <p>
                    我们相信年轻就是资本，敢想敢做，不怕试错。
                    每一行代码都是我们成长的足迹，每一个产品都是我们学习的成果。
                  </p>
                  <p>
                    虽然团队很小，但我们对技术的追求和对产品的用心绝不含糊。
                    我们希望用我们的方式，为这个世界带来一点点改变。
                  </p>
                </div>
              </div>
              <div className="relative">
                <div className="bg-gradient-to-br from-accent-500/10 to-primary-500/10 rounded-2xl p-8 border border-accent-500/20">
                  <div className="grid grid-cols-2 gap-6">
                    <div className="text-center">
                      <div className="text-3xl font-bold gradient-text mb-2">2</div>
                      <div className="text-gray-500 text-sm">团队成员</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold gradient-text mb-2">1</div>
                      <div className="text-gray-500 text-sm">在线产品</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold gradient-text mb-2">2025</div>
                      <div className="text-gray-500 text-sm">成立年份</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold gradient-text mb-2">∞</div>
                      <div className="text-gray-500 text-sm">学习热情</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </GlassCard>
        </div>
    </section>
  )
}

export default CompanyVision
