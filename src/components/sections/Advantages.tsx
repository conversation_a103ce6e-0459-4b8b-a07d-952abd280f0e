'use client'

import React from 'react'
import { Users, Zap, Heart, Shield, Clock, Award } from 'lucide-react'
import GlassCard from '@/components/common/GlassCard'

const advantages = [
  {
    icon: Heart,
    title: '用心设计',
    description: '我们相信好的产品应该简洁易用，每个功能都经过深思熟虑'
  },
  {
    icon: Users,
    title: '用户导向',
    description: '以用户体验为核心，持续收集反馈并不断改进产品'
  },
  {
    icon: Zap,
    title: '快速迭代',
    description: '小团队的优势是灵活快速，能够快速响应用户需求和市场变化'
  },
  {
    icon: Shield,
    title: '稳定可靠',
    description: '虽然我们是初创团队，但对代码质量和产品稳定性绝不妥协'
  },
  {
    icon: Clock,
    title: '持续更新',
    description: '我们承诺持续维护和更新产品，让用户体验不断提升'
  },
  {
    icon: Award,
    title: '学习成长',
    description: '我们保持学习的热情，不断提升技术能力和产品思维'
  }
]

const Advantages: React.FC = () => {
  return (
    <section className="relative py-24 md:py-32">
      <div className="container-custom">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-800 mb-6">
            我们的优势
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            虽然我们是初创团队，但我们有着独特的优势和坚定的信念
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {advantages.map((advantage, index) => {
            const Icon = advantage.icon
            return (
              <GlassCard
                key={advantage.title}
                className="text-center group"
                variant="default"
              >
                <div className="w-16 h-16 bg-gradient-to-br from-accent-500/20 to-primary-500/20 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                  <Icon size={32} className="text-accent-400" />
                </div>
                <h3 className="text-xl font-semibold text-gray-800 mb-4">
                  {advantage.title}
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  {advantage.description}
                </p>
              </GlassCard>
            )
          })}
        </div>

        {/* Stats Section */}
        <div className="mt-20 pt-16 border-t border-gray-200">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-3xl md:text-4xl font-bold gradient-text mb-2">98%</div>
              <div className="text-gray-500">客户满意度</div>
            </div>
            <div>
              <div className="text-3xl md:text-4xl font-bold gradient-text mb-2">24/7</div>
              <div className="text-gray-500">技术支持</div>
            </div>
            <div>
              <div className="text-3xl md:text-4xl font-bold gradient-text mb-2">100%</div>
              <div className="text-gray-500">按时交付</div>
            </div>
            <div>
              <div className="text-3xl md:text-4xl font-bold gradient-text mb-2">2025</div>
              <div className="text-gray-500">成立年份</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Advantages
