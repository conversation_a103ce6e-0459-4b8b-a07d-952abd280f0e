import React from 'react'
import { Smartphone, Globe, Lightbulb } from 'lucide-react'
import Section from '@/components/ui/Section'

const ProductsHero: React.FC = () => {
  return (
    <Section padding="xl" background="gradient">
      <div className="text-center mb-16">
        <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6">
          我们的产品
        </h1>
        <p className="text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed mb-8">
          我们专注于开发实用、简洁的数字化产品，
          为用户提供便捷的Web和移动应用体验
        </p>
        <div className="inline-flex items-center px-4 py-2 bg-accent-500/10 border border-accent-500/20 rounded-full text-accent-400 text-sm">
          <Lightbulb size={16} className="mr-2" />
          持续创新，用心打造每一个产品
        </div>
      </div>

      {/* 产品类型 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
        <div className="text-center group">
          <div className="w-16 h-16 bg-gradient-to-br from-accent-500/20 to-primary-500/20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
            <Globe size={32} className="text-accent-400" />
          </div>
          <h3 className="text-lg font-semibold text-white mb-2">
            Web 应用
          </h3>
          <p className="text-gray-400 text-sm">
            基于现代Web技术开发的在线应用，随时随地访问使用
          </p>
        </div>
        <div className="text-center group">
          <div className="w-16 h-16 bg-gradient-to-br from-accent-500/20 to-primary-500/20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
            <Smartphone size={32} className="text-accent-400" />
          </div>
          <h3 className="text-lg font-semibold text-white mb-2">
            移动应用
          </h3>
          <p className="text-gray-400 text-sm">
            为iOS和Android平台开发的原生移动应用
          </p>
        </div>
      </div>

      {/* 开发理念 */}
      <div className="mt-20 pt-16 border-t border-dark-700">
        <div className="text-center mb-8">
          <h2 className="text-2xl md:text-3xl font-bold text-white mb-4">
            我们的开发理念
          </h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
          <div className="text-center">
            <div className="text-accent-400 font-semibold mb-2">简洁实用</div>
            <div className="text-white mb-2">功能导向</div>
            <div className="text-gray-400 text-sm">专注核心功能，避免复杂冗余</div>
          </div>
          <div className="text-center">
            <div className="text-accent-400 font-semibold mb-2">用户体验</div>
            <div className="text-white mb-2">易用性优先</div>
            <div className="text-gray-400 text-sm">直观的界面设计，流畅的交互体验</div>
          </div>
          <div className="text-center">
            <div className="text-accent-400 font-semibold mb-2">持续改进</div>
            <div className="text-white mb-2">迭代优化</div>
            <div className="text-gray-400 text-sm">根据用户反馈不断完善产品</div>
          </div>
        </div>
      </div>
    </Section>
  )
}

export default ProductsHero
