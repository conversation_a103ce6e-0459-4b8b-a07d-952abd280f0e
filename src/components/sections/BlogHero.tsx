import React from 'react'
import { BookOpen, Code, Lightbulb, TrendingUp } from 'lucide-react'
import Section from '@/components/ui/Section'

const categories = [
  {
    icon: Code,
    name: '前端开发',
    description: 'React, Vue, Next.js 等前端技术',
    count: '15'
  },
  {
    icon: TrendingUp,
    name: '系统架构',
    description: '微服务、分布式系统设计',
    count: '12'
  },
  {
    icon: Lightbulb,
    name: '人工智能',
    description: 'AI 工具与应用实践',
    count: '8'
  },
  {
    icon: BookOpen,
    name: '开发经验',
    description: '项目实战与最佳实践',
    count: '20'
  }
]

const BlogHero: React.FC = () => {
  return (
    <Section padding="xl" background="gradient">
      <div className="text-center mb-16">
        <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6">
          技术博客
        </h1>
        <p className="text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed mb-8">
          分享最新的技术趋势、开发经验和行业洞察，
          与您一起探索软件开发的无限可能
        </p>
        <div className="inline-flex items-center px-4 py-2 bg-accent-500/10 border border-accent-500/20 rounded-full text-accent-400 text-sm">
          <BookOpen size={16} className="mr-2" />
          持续更新中，欢迎订阅
        </div>
      </div>

      {/* 分类统计 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        {categories.map((category) => {
          const Icon = category.icon
          return (
            <div key={category.name} className="text-center group">
              <div className="w-16 h-16 bg-gradient-to-br from-accent-500/20 to-primary-500/20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                <Icon size={32} className="text-accent-400" />
              </div>
              <h3 className="text-lg font-semibold text-white mb-2">
                {category.name}
              </h3>
              <p className="text-gray-400 text-sm mb-2">
                {category.description}
              </p>
              <div className="text-accent-400 font-medium">
                {category.count} 篇文章
              </div>
            </div>
          )
        })}
      </div>

      {/* 博客统计 */}
      <div className="mt-20 pt-16 border-t border-dark-700">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
          <div>
            <div className="text-3xl md:text-4xl font-bold gradient-text mb-2">55+</div>
            <div className="text-gray-400">技术文章</div>
          </div>
          <div>
            <div className="text-3xl md:text-4xl font-bold gradient-text mb-2">10万+</div>
            <div className="text-gray-400">阅读量</div>
          </div>
          <div>
            <div className="text-3xl md:text-4xl font-bold gradient-text mb-2">500+</div>
            <div className="text-gray-400">技术分享</div>
          </div>
          <div>
            <div className="text-3xl md:text-4xl font-bold gradient-text mb-2">每周</div>
            <div className="text-gray-400">更新频率</div>
          </div>
        </div>
      </div>
    </Section>
  )
}

export default BlogHero
