'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import { Calendar, Clock, User, Tag, ArrowRight, Search } from 'lucide-react'
import Section from '@/components/ui/Section'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import { BlogPostMeta } from '@/lib/blog'
import { formatDate } from '@/lib/utils'

interface BlogListProps {
  posts: BlogPostMeta[]
}

const categories = ['全部', '前端开发', '系统架构', '人工智能', '开发经验', '移动开发']

const BlogList: React.FC<BlogListProps> = ({ posts }) => {
  const [selectedCategory, setSelectedCategory] = useState('全部')
  const [searchTerm, setSearchTerm] = useState('')

  const filteredPosts = posts.filter(post => {
    const matchesCategory = selectedCategory === '全部' || post.category === selectedCategory
    const matchesSearch = post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         post.excerpt.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         post.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
    return matchesCategory && matchesSearch
  })

  const featuredPosts = posts.filter(post => post.featured)
  const regularPosts = filteredPosts.filter(post => !post.featured)

  return (
    <Section padding="xl">
      <div className="text-center mb-16">
        <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-6">
          最新文章
        </h2>
        <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-8">
          探索技术前沿，分享实战经验
        </p>

        {/* 搜索和筛选 */}
        <div className="flex flex-col md:flex-row gap-4 justify-center items-center mb-8">
          {/* 搜索框 */}
          <div className="relative">
            <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="搜索文章..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 bg-dark-800 border border-dark-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-accent-500 w-64"
            />
          </div>

          {/* 分类筛选 */}
          <div className="flex flex-wrap justify-center gap-2">
            {categories.map((category) => (
              <button
                key={category}
                onClick={() => setSelectedCategory(category)}
                className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${
                  selectedCategory === category
                    ? 'bg-accent-500 text-white'
                    : 'bg-dark-800 text-gray-300 hover:bg-dark-700 hover:text-white'
                }`}
              >
                {category}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* 精选文章 */}
      {featuredPosts.length > 0 && selectedCategory === '全部' && !searchTerm && (
        <div className="mb-16">
          <h3 className="text-2xl font-bold text-white mb-8 text-center">精选文章</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {featuredPosts.slice(0, 2).map((post) => (
              <Card key={post.slug} className="group h-full">
                <CardHeader>
                  <div className="flex items-center justify-between mb-3">
                    <span className="px-3 py-1 bg-accent-500 text-white text-xs font-medium rounded-full">
                      精选
                    </span>
                    <div className="flex items-center text-gray-400 text-sm">
                      <Clock size={14} className="mr-1" />
                      {post.readTime}
                    </div>
                  </div>
                  <CardTitle className="text-xl group-hover:text-accent-400 transition-colors duration-200">
                    <Link href={`/blog/${post.slug}`}>
                      {post.title}
                    </Link>
                  </CardTitle>
                  <div className="flex items-center text-gray-400 text-sm space-x-4">
                    <div className="flex items-center">
                      <User size={14} className="mr-1" />
                      {post.author}
                    </div>
                    <div className="flex items-center">
                      <Calendar size={14} className="mr-1" />
                      {formatDate(post.date)}
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <CardDescription className="mb-4 leading-relaxed">
                    {post.excerpt}
                  </CardDescription>
                  
                  {/* 标签 */}
                  <div className="flex flex-wrap gap-2 mb-4">
                    {post.tags.slice(0, 3).map((tag) => (
                      <span
                        key={tag}
                        className="px-2 py-1 bg-dark-700 text-gray-300 text-xs rounded flex items-center"
                      >
                        <Tag size={10} className="mr-1" />
                        {tag}
                      </span>
                    ))}
                  </div>

                  <Link
                    href={`/blog/${post.slug}`}
                    className="text-accent-400 hover:text-accent-300 transition-colors duration-200 flex items-center text-sm font-medium"
                  >
                    阅读全文
                    <ArrowRight size={14} className="ml-1" />
                  </Link>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* 文章列表 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {regularPosts.map((post) => (
          <Card key={post.slug} className="group h-full">
            <CardHeader>
              <div className="flex items-center justify-between mb-3">
                <span className="px-3 py-1 bg-accent-500/10 text-accent-400 text-xs font-medium rounded-full">
                  {post.category}
                </span>
                <div className="flex items-center text-gray-400 text-sm">
                  <Clock size={14} className="mr-1" />
                  {post.readTime}
                </div>
              </div>
              <CardTitle className="text-lg group-hover:text-accent-400 transition-colors duration-200">
                <Link href={`/blog/${post.slug}`}>
                  {post.title}
                </Link>
              </CardTitle>
              <div className="flex items-center text-gray-400 text-sm space-x-4">
                <div className="flex items-center">
                  <User size={14} className="mr-1" />
                  {post.author}
                </div>
                <div className="flex items-center">
                  <Calendar size={14} className="mr-1" />
                  {formatDate(post.date)}
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <CardDescription className="mb-4 leading-relaxed">
                {post.excerpt}
              </CardDescription>
              
              {/* 标签 */}
              <div className="flex flex-wrap gap-2 mb-4">
                {post.tags.slice(0, 2).map((tag) => (
                  <span
                    key={tag}
                    className="px-2 py-1 bg-dark-700 text-gray-300 text-xs rounded flex items-center"
                  >
                    <Tag size={10} className="mr-1" />
                    {tag}
                  </span>
                ))}
                {post.tags.length > 2 && (
                  <span className="px-2 py-1 bg-dark-700 text-gray-300 text-xs rounded">
                    +{post.tags.length - 2}
                  </span>
                )}
              </div>

              <Link
                href={`/blog/${post.slug}`}
                className="text-accent-400 hover:text-accent-300 transition-colors duration-200 flex items-center text-sm font-medium"
              >
                阅读全文
                <ArrowRight size={14} className="ml-1" />
              </Link>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* 无结果提示 */}
      {filteredPosts.length === 0 && (
        <div className="text-center py-16">
          <div className="text-gray-400 text-lg mb-4">
            没有找到相关文章
          </div>
          <Button
            variant="outline"
            onClick={() => {
              setSelectedCategory('全部')
              setSearchTerm('')
            }}
          >
            重置筛选
          </Button>
        </div>
      )}

      {/* 订阅提示 */}
      <div className="mt-16 bg-dark-800/50 backdrop-blur-sm border border-dark-700 rounded-xl p-8 text-center">
        <h3 className="text-2xl font-bold text-white mb-4">
          订阅我们的技术博客
        </h3>
        <p className="text-gray-300 mb-6 max-w-2xl mx-auto">
          第一时间获取最新的技术文章和行业洞察，与我们一起成长
        </p>
        <Button size="lg" href="/contact">
          联系我们
        </Button>
      </div>
    </Section>
  )
}

export default BlogList
