import React from 'react'
import { Check, Star, ArrowRight } from 'lucide-react'
import Section from '@/components/ui/Section'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import Button from '@/components/ui/Button'

const pricingPlans = [
  {
    name: '基础版',
    price: '5万起',
    period: '项目',
    description: '适合小型企业和初创公司的基础软件开发需求',
    features: [
      '需求分析与设计',
      '基础功能开发',
      '响应式界面',
      '基础测试',
      '部署上线',
      '3个月技术支持'
    ],
    popular: false,
    buttonText: '立即咨询',
    color: 'border-dark-700'
  },
  {
    name: '专业版',
    price: '15万起',
    period: '项目',
    description: '适合中型企业的复杂业务系统开发',
    features: [
      '深度需求分析',
      '完整功能开发',
      '高级UI/UX设计',
      '全面测试覆盖',
      '性能优化',
      '系统集成',
      '6个月技术支持',
      '培训服务'
    ],
    popular: true,
    buttonText: '立即咨询',
    color: 'border-accent-500'
  },
  {
    name: '企业版',
    price: '30万起',
    period: '项目',
    description: '适合大型企业的复杂系统和平台开发',
    features: [
      '企业级架构设计',
      '微服务开发',
      '高可用部署',
      '安全加固',
      '性能调优',
      '多系统集成',
      '1年技术支持',
      '专属技术顾问',
      '定制化培训'
    ],
    popular: false,
    buttonText: '立即咨询',
    color: 'border-dark-700'
  }
]

const ServicePricing: React.FC = () => {
  return (
    <Section padding="xl">
      <div className="text-center mb-16">
        <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-6">
          服务价格
        </h2>
        <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-8">
          透明的价格体系，选择最适合您企业规模和需求的服务方案
        </p>
        <div className="inline-flex items-center px-4 py-2 bg-accent-500/10 border border-accent-500/20 rounded-full text-accent-400 text-sm">
          <Star size={16} className="mr-2" />
          所有方案均可根据具体需求定制
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
        {pricingPlans.map((plan) => (
          <Card
            key={plan.name}
            className={`relative h-full ${plan.color} ${
              plan.popular ? 'scale-105 shadow-xl shadow-accent-500/20' : ''
            }`}
          >
            {plan.popular && (
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <div className="bg-gradient-to-r from-accent-500 to-primary-500 text-white px-4 py-1 rounded-full text-sm font-medium">
                  推荐方案
                </div>
              </div>
            )}

            <CardHeader className="text-center">
              <CardTitle className="text-xl mb-2">{plan.name}</CardTitle>
              <div className="mb-4">
                <span className="text-3xl font-bold gradient-text">{plan.price}</span>
                <span className="text-gray-400 ml-2">/ {plan.period}</span>
              </div>
              <CardDescription>{plan.description}</CardDescription>
            </CardHeader>

            <CardContent className="flex-grow flex flex-col">
              <div className="space-y-3 mb-8 flex-grow">
                {plan.features.map((feature) => (
                  <div key={feature} className="flex items-center">
                    <Check size={16} className="text-accent-400 mr-3 flex-shrink-0" />
                    <span className="text-gray-300 text-sm">{feature}</span>
                  </div>
                ))}
              </div>

              <Button
                variant={plan.popular ? 'primary' : 'outline'}
                className="w-full"
                href="/contact"
              >
                {plan.buttonText}
                <ArrowRight size={16} className="ml-2" />
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* 定制服务说明 */}
      <div className="bg-dark-800/50 backdrop-blur-sm border border-dark-700 rounded-xl p-8 text-center">
        <h3 className="text-2xl font-bold text-white mb-4">
          需要定制化解决方案？
        </h3>
        <p className="text-gray-300 mb-6 max-w-2xl mx-auto">
          我们理解每个企业都有独特的需求。如果标准方案不能完全满足您的要求，
          我们可以为您量身定制专属的技术解决方案。
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button size="lg" href="/contact">
            获取定制方案
          </Button>
          <Button variant="outline" size="lg" href="/portfolio">
            查看成功案例
          </Button>
        </div>
      </div>

      {/* FAQ 简要说明 */}
      <div className="mt-16 pt-16 border-t border-dark-700">
        <div className="text-center mb-12">
          <h3 className="text-2xl md:text-3xl font-bold text-white mb-4">
            常见问题
          </h3>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
          <div>
            <h4 className="text-lg font-semibold text-white mb-3">
              项目开发周期是多长？
            </h4>
            <p className="text-gray-300 text-sm">
              根据项目复杂度，一般在2-6个月之间。我们会在需求分析阶段给出准确的时间估算。
            </p>
          </div>
          <div>
            <h4 className="text-lg font-semibold text-white mb-3">
              是否提供源代码？
            </h4>
            <p className="text-gray-300 text-sm">
              是的，项目完成后我们会提供完整的源代码和相关文档。
            </p>
          </div>
          <div>
            <h4 className="text-lg font-semibold text-white mb-3">
              后期维护费用如何计算？
            </h4>
            <p className="text-gray-300 text-sm">
              免费维护期结束后，维护费用一般为项目总价的10-15%/年。
            </p>
          </div>
          <div>
            <h4 className="text-lg font-semibold text-white mb-3">
              支付方式是怎样的？
            </h4>
            <p className="text-gray-300 text-sm">
              通常采用分阶段付款：签约30%，开发完成60%，验收通过10%。
            </p>
          </div>
        </div>
      </div>
    </Section>
  )
}

export default ServicePricing
