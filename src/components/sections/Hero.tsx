'use client'

import React from 'react'
import { ArrowRight, Code, Zap, Users } from 'lucide-react'
import Button from '@/components/ui/Button'
import GlassCard from '@/components/common/GlassCard'
import { WaveIcon, TargetIcon, ThinkIcon, RocketIcon } from '@/components/icons/CustomIcons'

const Hero: React.FC = () => {
  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* 移除所有背景，使用全局背景 */}
      
      {/* 个性化浮动元素 - 手绘风格 */}
      <div className="absolute top-20 left-10 w-20 h-20 bg-accent-400/20 rounded-full animate-float" />
      <div className="absolute top-40 right-20 w-32 h-32 bg-primary-400/20 rounded-full animate-float" style={{ animationDelay: '2s' }} />
      <div className="absolute bottom-40 left-20 w-24 h-24 bg-secondary-400/20 rounded-full animate-float" style={{ animationDelay: '4s' }} />

      {/* 不规则几何图形 - 增加个性 */}
      <div className="absolute top-1/4 left-1/4 w-16 h-16 bg-accent-300/30 transform rotate-45 rounded-lg animate-float" style={{ animationDelay: '1s' }} />
      <div className="absolute top-3/4 right-1/4 w-12 h-12 bg-secondary-300/30 rounded-full rotate-12 animate-float" style={{ animationDelay: '3s' }} />
      <div className="absolute top-1/2 left-10 w-8 h-8 bg-primary-300/40 rounded-full animate-float" style={{ animationDelay: '5s' }} />
      <div className="absolute bottom-1/4 right-10 w-20 h-20 bg-accent-200/30 rounded-full animate-float" style={{ animationDelay: '6s' }} />

      {/* 活泼的装饰元素 */}
      <div className="absolute top-32 right-32 w-40 h-40 bg-gradient-to-br from-accent-300/20 to-secondary-300/20 backdrop-blur-sm rounded-3xl rotate-12 animate-float" style={{ animationDelay: '1.5s' }} />
      <div className="absolute bottom-32 left-32 w-32 h-32 bg-gradient-to-tl from-primary-300/20 to-accent-300/20 backdrop-blur-sm rounded-2xl -rotate-12 animate-float" style={{ animationDelay: '3.5s' }} />

      <div className="container-custom relative z-10">
        <div className="text-center max-w-4xl mx-auto">
          {/* Badge - 更加个人化 */}
          <div className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-accent-100 to-secondary-100 border-2 border-accent-300 rounded-full text-accent-600 text-sm font-medium mb-8 animate-fade-in shadow-lg">
            <WaveIcon size={16} className="mr-2" />
            嗨！我们是两个爱折腾的大学生
          </div>

          {/* Main Heading - 更加轻松 */}
          <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold text-gray-800 mb-6 animate-slide-up">
            用代码
            <br />
            <span className="bg-gradient-to-r from-primary-500 via-accent-500 to-secondary-500 bg-clip-text text-transparent">创造有趣的东西</span>
          </h1>

          {/* Subtitle - 更加个人化的表达 */}
          <p className="text-xl md:text-2xl text-gray-600 mb-8 leading-relaxed animate-slide-up" style={{ animationDelay: '0.2s' }}>
            我们是数峪科技 —— 一个由两名在读大学生组成的小团队
            <br className="hidden md:block" />
            我们喜欢用代码解决生活中的小问题，创造简单好用的产品
          </p>

          {/* CTA Buttons - 更加个人化 */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12 animate-slide-up" style={{ animationDelay: '0.4s' }}>
            <Button size="lg" href="/products" className="bg-gradient-to-r from-primary-500 to-accent-500 hover:from-primary-600 hover:to-accent-600 text-white shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 inline-flex items-center">
              <TargetIcon size={20} className="mr-2" />
              看看我们做了什么
              <ArrowRight size={20} className="ml-2" />
            </Button>
            <Button variant="outline" size="lg" href="/about" className="border-2 border-secondary-400 text-secondary-600 hover:bg-secondary-50 shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-300 inline-flex items-center">
              <ThinkIcon size={20} className="mr-2" />
              想了解我们？
            </Button>
          </div>

          {/* Stats - 使用新的GlassCard组件 */}
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-8 max-w-2xl mx-auto animate-slide-up" style={{ animationDelay: '0.6s' }}>
            <GlassCard variant="primary" className="text-center">
              <div className="flex items-center justify-center w-14 h-14 bg-gradient-to-br from-primary-400 to-primary-500 rounded-full mx-auto mb-3 shadow-md">
                <Code size={24} className="text-white" />
              </div>
              <div className="text-2xl font-bold text-gray-800 mb-1">1</div>
              <div className="text-gray-600 text-sm">正在开发的产品</div>
            </GlassCard>
            <GlassCard variant="accent" className="text-center">
              <div className="flex items-center justify-center w-14 h-14 bg-gradient-to-br from-accent-400 to-accent-500 rounded-full mx-auto mb-3 shadow-md">
                <Users size={24} className="text-white" />
              </div>
              <div className="text-2xl font-bold text-gray-800 mb-1">2</div>
              <div className="text-gray-600 text-sm">小而美的团队</div>
            </GlassCard>
            <GlassCard variant="secondary" className="text-center">
              <div className="flex items-center justify-center w-14 h-14 bg-gradient-to-br from-secondary-400 to-secondary-500 rounded-full mx-auto mb-3 shadow-md">
                <Zap size={24} className="text-white" />
              </div>
              <div className="text-2xl font-bold text-gray-800 mb-1">2025</div>
              <div className="text-gray-600 text-sm">开始追梦的年份</div>
            </GlassCard>
          </div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <div className="w-6 h-10 border-2 border-gray-400 rounded-full flex justify-center">
          <div className="w-1 h-3 bg-gray-400 rounded-full mt-2 animate-pulse" />
        </div>
      </div>
    </section>
  )
}

export default Hero
