'use client'

import React from 'react'
import { ExternalLink, Calendar, Tag, Users } from 'lucide-react'
import Section from '@/components/ui/Section'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import Button from '@/components/ui/Button'

const portfolioItems = [
  {
    id: 1,
    title: '智慧零售管理系统',
    category: '企业管理系统',
    client: '某大型连锁零售企业',
    description: '为大型连锁零售企业打造的全方位管理系统，包含库存管理、销售分析、会员管理等核心功能。',
    image: '/images/portfolio/retail-system.jpg',
    technologies: ['React', 'Node.js', 'MySQL', 'Redis'],
    features: ['实时库存管理', '销售数据分析', '会员积分系统', '多店铺管理'],
    results: ['提升运营效率40%', '降低库存成本25%', '增加会员复购率30%'],
    duration: '6个月',
    teamSize: '8人',
    status: '已上线',
    link: '#',
    isExternal: false
  },
  {
    id: 2,
    title: '在线教育平台',
    category: '教育科技',
    client: '某知名教育机构',
    description: '集课程管理、在线学习、考试评估于一体的综合性在线教育平台，支持多种学习模式。',
    image: '/images/portfolio/education-platform.jpg',
    technologies: ['Vue.js', 'Spring Boot', 'PostgreSQL', 'WebRTC'],
    features: ['直播授课', '作业批改', '学习进度跟踪', '智能推荐'],
    results: ['学员满意度95%', '课程完成率提升50%', '教学效率提升35%'],
    duration: '8个月',
    teamSize: '12人',
    status: '已上线',
    link: '#',
    isExternal: false
  },
  {
    id: 3,
    title: '智能物流配送App',
    category: '移动应用',
    client: '某物流科技公司',
    description: '为物流配送员和客户提供的移动应用，实现订单管理、路线优化、实时跟踪等功能。',
    image: '/images/portfolio/logistics-app.jpg',
    technologies: ['React Native', 'Express.js', 'MongoDB', 'GPS API'],
    features: ['智能路线规划', '实时位置跟踪', '电子签收', '配送统计'],
    results: ['配送效率提升45%', '客户满意度98%', '运营成本降低30%'],
    duration: '4个月',
    teamSize: '6人',
    status: '已上线',
    link: '#',
    isExternal: false
  },
  {
    id: 4,
    title: '医疗预约管理系统',
    category: '医疗健康',
    client: '某三甲医院',
    description: '为医院打造的智能预约管理系统，优化就医流程，提升患者就医体验。',
    image: '/images/portfolio/medical-system.jpg',
    technologies: ['Angular', 'Java', 'Oracle', '微信小程序'],
    features: ['在线预约挂号', '排队叫号', '病历管理', '支付结算'],
    results: ['预约成功率99%', '等待时间减少60%', '患者满意度96%'],
    duration: '10个月',
    teamSize: '15人',
    status: '已上线',
    link: '#',
    isExternal: false
  },
  {
    id: 5,
    title: '企业财务管理平台',
    category: '金融科技',
    client: '某中型制造企业',
    description: '集成财务核算、预算管理、报表分析的企业级财务管理平台。',
    image: '/images/portfolio/finance-platform.jpg',
    technologies: ['React', 'Spring Cloud', 'MySQL', 'Elasticsearch'],
    features: ['财务核算', '预算控制', '报表生成', '风险预警'],
    results: ['财务处理效率提升60%', '报表生成时间缩短80%', '成本控制精度提升40%'],
    duration: '7个月',
    teamSize: '10人',
    status: '已上线',
    link: '#',
    isExternal: false
  },
  {
    id: 6,
    title: '社交电商小程序',
    category: '电商平台',
    client: '某新零售公司',
    description: '基于微信生态的社交电商小程序，融合社交分享和电商购物功能。',
    image: '/images/portfolio/social-ecommerce.jpg',
    technologies: ['微信小程序', 'Node.js', 'MongoDB', '微信支付'],
    features: ['社交分享', '拼团购买', '分销推广', '会员体系'],
    results: ['用户增长300%', '转化率提升25%', '复购率提升40%'],
    duration: '5个月',
    teamSize: '8人',
    status: '已上线',
    link: '#',
    isExternal: false
  }
]

const categories = ['全部', '企业管理系统', '移动应用', '教育科技', '医疗健康', '金融科技', '电商平台']

const PortfolioGrid: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = React.useState('全部')

  const filteredItems = selectedCategory === '全部' 
    ? portfolioItems 
    : portfolioItems.filter(item => item.category === selectedCategory)

  return (
    <Section padding="xl">
      <div className="text-center mb-16">
        <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-6">
          项目案例
        </h2>
        <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-8">
          精选部分成功项目案例，展示我们在不同领域的技术实力和项目经验
        </p>

        {/* 分类筛选 */}
        <div className="flex flex-wrap justify-center gap-3">
          {categories.map((category) => (
            <button
              key={category}
              onClick={() => setSelectedCategory(category)}
              className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${
                selectedCategory === category
                  ? 'bg-accent-500 text-white'
                  : 'bg-dark-800 text-gray-300 hover:bg-dark-700 hover:text-white'
              }`}
            >
              {category}
            </button>
          ))}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {filteredItems.map((item) => (
          <Card key={item.id} className="group h-full flex flex-col">
            {/* 项目图片占位 */}
            <div className="h-48 bg-gradient-to-br from-accent-500/10 to-primary-500/10 rounded-t-xl flex items-center justify-center mb-4">
              <div className="text-accent-400 text-4xl font-bold">
                {item.title.charAt(0)}
              </div>
            </div>

            <CardHeader className="flex-grow">
              <div className="flex items-center justify-between mb-2">
                <span className="px-2 py-1 bg-accent-500/10 text-accent-400 text-xs rounded-full">
                  {item.category}
                </span>
                <span className="text-gray-400 text-xs">{item.status}</span>
              </div>
              <CardTitle className="text-lg group-hover:text-accent-400 transition-colors duration-200">
                {item.title}
              </CardTitle>
              <div className="text-sm text-gray-400 mb-2">
                客户：{item.client}
              </div>
              <CardDescription className="leading-relaxed">
                {item.description}
              </CardDescription>
            </CardHeader>

            <CardContent>
              {/* 核心功能 */}
              <div className="mb-4">
                <h4 className="text-sm font-semibold text-white mb-2">核心功能</h4>
                <div className="flex flex-wrap gap-1">
                  {item.features.slice(0, 3).map((feature) => (
                    <span
                      key={feature}
                      className="px-2 py-1 bg-dark-700 text-gray-300 text-xs rounded"
                    >
                      {feature}
                    </span>
                  ))}
                  {item.features.length > 3 && (
                    <span className="px-2 py-1 bg-dark-700 text-gray-300 text-xs rounded">
                      +{item.features.length - 3}
                    </span>
                  )}
                </div>
              </div>

              {/* 技术栈 */}
              <div className="mb-4">
                <h4 className="text-sm font-semibold text-white mb-2">技术栈</h4>
                <div className="flex flex-wrap gap-1">
                  {item.technologies.map((tech) => (
                    <span
                      key={tech}
                      className="px-2 py-1 bg-accent-500/10 text-accent-400 text-xs rounded border border-accent-500/20"
                    >
                      {tech}
                    </span>
                  ))}
                </div>
              </div>

              {/* 项目信息 */}
              <div className="grid grid-cols-2 gap-4 mb-4 text-xs text-gray-400">
                <div className="flex items-center">
                  <Calendar size={12} className="mr-1" />
                  {item.duration}
                </div>
                <div className="flex items-center">
                  <Users size={12} className="mr-1" />
                  {item.teamSize}
                </div>
              </div>

              {/* 查看详情按钮 */}
              <Button
                variant="outline"
                size="sm"
                className="w-full"
                href={item.link}
                external={item.isExternal}
              >
                查看详情
                {item.isExternal && <ExternalLink size={14} className="ml-2" />}
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* 更多案例 */}
      <div className="text-center mt-16">
        <div className="bg-dark-800/50 backdrop-blur-sm border border-dark-700 rounded-xl p-8">
          <h3 className="text-2xl font-bold text-white mb-4">
            想了解更多案例？
          </h3>
          <p className="text-gray-300 mb-6 max-w-2xl mx-auto">
            我们还有更多成功案例等待与您分享。联系我们，了解我们如何为您的企业提供定制化的技术解决方案。
          </p>
          <Button size="lg" href="/contact">
            联系我们
          </Button>
        </div>
      </div>
    </Section>
  )
}

export default PortfolioGrid
