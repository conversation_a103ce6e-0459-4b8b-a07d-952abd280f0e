import React from 'react'
import { Github, Linkedin, Mail } from 'lucide-react'
import Section from '@/components/ui/Section'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'

const teamMembers = [
  {
    name: '小张',
    position: '联合创始人',
    bio: '计算机科学专业在读大学生，对Web开发充满热情，主要负责前端开发和产品设计。',
    avatar: '/images/team/member1.jpg',
    skills: ['React', 'TypeScript', 'UI设计', 'Web开发'],
    social: {
      github: '#',
      linkedin: '#',
      email: '<EMAIL>'
    }
  },
  {
    name: '小李',
    position: '联合创始人',
    bio: '软件工程专业在读大学生，喜欢钻研技术，主要负责后端开发和系统架构。',
    avatar: '/images/team/member2.jpg',
    skills: ['Node.js', 'Python', '数据库', '后端开发'],
    social: {
      github: '#',
      linkedin: '#',
      email: '<EMAIL>'
    }
  }
]

const TeamIntroduction: React.FC = () => {
  return (
    <Section padding="xl">
      <div className="text-center mb-16">
        <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-6">
          我们的团队
        </h2>
        <p className="text-xl text-gray-300 max-w-3xl mx-auto">
          我们是两名充满热情的在读大学生，虽然经验有限，
          但我们对技术充满好奇心，愿意不断学习和成长
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
        {teamMembers.map((member) => (
          <Card key={member.name} className="text-center group">
            <CardHeader>
              <div className="w-24 h-24 bg-gradient-to-br from-accent-500/20 to-primary-500/20 rounded-full mx-auto mb-4 flex items-center justify-center">
                <div className="w-20 h-20 bg-dark-700 rounded-full flex items-center justify-center">
                  <span className="text-2xl font-bold text-accent-400">
                    {member.name.charAt(0)}
                  </span>
                </div>
              </div>
              <CardTitle className="text-lg">{member.name}</CardTitle>
              <div className="text-accent-400 text-sm font-medium">
                {member.position}
              </div>
            </CardHeader>
            <CardContent>
              <CardDescription className="mb-4 leading-relaxed">
                {member.bio}
              </CardDescription>
              
              {/* 技能标签 */}
              <div className="flex flex-wrap gap-2 justify-center mb-4">
                {member.skills.map((skill) => (
                  <span
                    key={skill}
                    className="px-2 py-1 bg-accent-500/10 text-accent-400 text-xs rounded-full"
                  >
                    {skill}
                  </span>
                ))}
              </div>

              {/* 社交链接 */}
              <div className="flex justify-center space-x-3">
                <a
                  href={member.social.github}
                  className="text-gray-400 hover:text-accent-400 transition-colors duration-200"
                  aria-label="GitHub"
                >
                  <Github size={18} />
                </a>
                <a
                  href={member.social.linkedin}
                  className="text-gray-400 hover:text-accent-400 transition-colors duration-200"
                  aria-label="LinkedIn"
                >
                  <Linkedin size={18} />
                </a>
                <a
                  href={`mailto:${member.social.email}`}
                  className="text-gray-400 hover:text-accent-400 transition-colors duration-200"
                  aria-label="Email"
                >
                  <Mail size={18} />
                </a>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* 团队文化 */}
      <div className="mt-20 pt-16 border-t border-dark-700">
        <div className="text-center mb-12">
          <h3 className="text-2xl md:text-3xl font-bold text-white mb-4">
            团队文化
          </h3>
          <p className="text-gray-300 max-w-2xl mx-auto">
            我们相信优秀的团队文化是成功的基石
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          <div className="text-center">
            <div className="w-12 h-12 bg-accent-500/10 rounded-lg flex items-center justify-center mx-auto mb-4">
              <span className="text-accent-400 font-bold">学</span>
            </div>
            <h4 className="text-white font-semibold mb-2">持续学习</h4>
            <p className="text-gray-400 text-sm">
              鼓励团队成员不断学习新技术，保持技术敏锐度
            </p>
          </div>
          <div className="text-center">
            <div className="w-12 h-12 bg-accent-500/10 rounded-lg flex items-center justify-center mx-auto mb-4">
              <span className="text-accent-400 font-bold">创</span>
            </div>
            <h4 className="text-white font-semibold mb-2">勇于创新</h4>
            <p className="text-gray-400 text-sm">
              鼓励创新思维，勇于尝试新的解决方案和技术
            </p>
          </div>
          <div className="text-center">
            <div className="w-12 h-12 bg-accent-500/10 rounded-lg flex items-center justify-center mx-auto mb-4">
              <span className="text-accent-400 font-bold">合</span>
            </div>
            <h4 className="text-white font-semibold mb-2">团队协作</h4>
            <p className="text-gray-400 text-sm">
              重视团队合作，共同解决问题，分享知识和经验
            </p>
          </div>
          <div className="text-center">
            <div className="w-12 h-12 bg-accent-500/10 rounded-lg flex items-center justify-center mx-auto mb-4">
              <span className="text-accent-400 font-bold">责</span>
            </div>
            <h4 className="text-white font-semibold mb-2">责任担当</h4>
            <p className="text-gray-400 text-sm">
              对工作负责，对客户负责，对团队负责
            </p>
          </div>
        </div>
      </div>
    </Section>
  )
}

export default TeamIntroduction
