import React from 'react'
import { Code2, Smartphone, Settings, Database } from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import Section from '@/components/ui/Section'
import Button from '@/components/ui/Button'

const services = [
  {
    icon: Code2,
    title: '定制软件开发',
    description: '基于您的业务需求，打造专属的企业级软件解决方案，提升运营效率和竞争优势。',
    features: ['企业管理系统', 'Web应用开发', '系统架构设计', '代码质量保证']
  },
  {
    icon: Smartphone,
    title: '移动应用开发',
    description: '跨平台移动应用开发，为iOS和Android平台提供原生体验的移动解决方案。',
    features: ['原生应用开发', '跨平台方案', 'UI/UX设计', '应用商店发布']
  },
  {
    icon: Settings,
    title: '技术咨询服务',
    description: '专业的技术咨询和架构设计服务，帮助企业制定最适合的技术发展策略。',
    features: ['技术选型建议', '架构设计咨询', '代码审查', '性能优化']
  },
  {
    icon: Database,
    title: '系统集成',
    description: '整合现有系统和第三方服务，构建统一的数据平台和业务流程。',
    features: ['API集成开发', '数据迁移', '系统对接', '云服务部署']
  }
]

const Services: React.FC = () => {
  return (
    <Section id="services" padding="xl">
      <div className="text-center mb-16">
        <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-6">
          核心服务
        </h2>
        <p className="text-xl text-gray-300 max-w-3xl mx-auto">
          我们提供全方位的软件开发服务，从需求分析到产品交付，
          为您的企业数字化转型提供强有力的技术支持
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
        {services.map((service, index) => {
          const Icon = service.icon
          return (
            <Card key={service.title} className="group h-full">
              <CardHeader>
                <div className="w-12 h-12 bg-accent-500/10 rounded-lg flex items-center justify-center mb-4 group-hover:bg-accent-500/20 transition-colors duration-300">
                  <Icon size={24} className="text-accent-400" />
                </div>
                <CardTitle className="text-lg">{service.title}</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="mb-4">
                  {service.description}
                </CardDescription>
                <ul className="space-y-2">
                  {service.features.map((feature) => (
                    <li key={feature} className="flex items-center text-sm text-gray-400">
                      <div className="w-1.5 h-1.5 bg-accent-400 rounded-full mr-3" />
                      {feature}
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          )
        })}
      </div>

      <div className="text-center">
        <Button href="/services" variant="outline" size="lg">
          了解更多服务详情
        </Button>
      </div>
    </Section>
  )
}

export default Services
