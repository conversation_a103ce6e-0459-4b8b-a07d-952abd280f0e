import React from 'react'
import { Code2, Smartphone, Settings, Database, CheckCircle } from 'lucide-react'
import Section from '@/components/ui/Section'

const services = [
  {
    id: 'custom-development',
    icon: Code2,
    title: '定制软件开发',
    subtitle: '企业级软件解决方案',
    description: '基于您的业务需求，打造专属的企业级软件解决方案。我们拥有丰富的行业经验，能够为不同规模的企业提供高质量、可扩展的软件产品。',
    features: [
      '需求分析与系统设计',
      '前端界面开发',
      '后端服务架构',
      '数据库设计与优化',
      '第三方系统集成',
      '性能优化与安全加固',
      '部署与运维支持',
      '持续维护与升级'
    ],
    technologies: ['Java', 'Spring Boot', 'React', 'Vue.js', 'MySQL', 'Redis', 'Docker', 'Kubernetes'],
    benefits: [
      '提升业务效率',
      '降低运营成本',
      '增强竞争优势',
      '支持业务扩展'
    ]
  },
  {
    id: 'mobile-development',
    icon: Smartphone,
    title: '移动应用开发',
    subtitle: 'iOS & Android 原生应用',
    description: '专业的移动应用开发服务，为iOS和Android平台提供原生体验的移动解决方案。从UI/UX设计到应用商店发布，我们提供全流程服务。',
    features: [
      '原生应用开发',
      '跨平台解决方案',
      'UI/UX设计',
      'API接口开发',
      '第三方SDK集成',
      '应用性能优化',
      '应用商店发布',
      '用户反馈与迭代'
    ],
    technologies: ['Swift', 'Kotlin', 'React Native', 'Flutter', 'Firebase', 'AWS Mobile', 'TestFlight', 'Google Play'],
    benefits: [
      '扩大用户覆盖',
      '提升用户体验',
      '增强品牌影响力',
      '开拓移动市场'
    ]
  },
  {
    id: 'consulting',
    icon: Settings,
    title: '技术咨询服务',
    subtitle: '架构设计与技术选型',
    description: '专业的技术咨询和架构设计服务，帮助企业制定最适合的技术发展策略。我们的专家团队将为您提供全方位的技术指导和建议。',
    features: [
      '技术选型建议',
      '系统架构设计',
      '代码审查与优化',
      '性能评估与调优',
      '安全评估与加固',
      '团队技术培训',
      '开发流程优化',
      '技术债务治理'
    ],
    technologies: ['微服务架构', '云原生', 'DevOps', 'CI/CD', '容器化', '监控告警', '自动化测试', '敏捷开发'],
    benefits: [
      '降低技术风险',
      '提升开发效率',
      '优化系统性能',
      '增强团队能力'
    ]
  },
  {
    id: 'integration',
    icon: Database,
    title: '系统集成',
    subtitle: '数据整合与系统对接',
    description: '整合现有系统和第三方服务，构建统一的数据平台和业务流程。我们帮助企业打破信息孤岛，实现系统间的无缝对接。',
    features: [
      'API接口开发',
      '数据迁移与同步',
      '系统间对接',
      '消息队列集成',
      '数据仓库建设',
      '报表系统开发',
      '云服务集成',
      '监控与运维'
    ],
    technologies: ['RESTful API', 'GraphQL', 'Message Queue', 'ETL', 'Data Pipeline', 'Elasticsearch', 'Kafka', 'Microservices'],
    benefits: [
      '消除信息孤岛',
      '提高数据质量',
      '简化业务流程',
      '支持决策分析'
    ]
  }
]

const ServiceDetails: React.FC = () => {
  return (
    <Section padding="xl">
      <div className="text-center mb-16">
        <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-6">
          服务详情
        </h2>
        <p className="text-xl text-gray-300 max-w-3xl mx-auto">
          深入了解我们的专业服务，选择最适合您企业需求的解决方案
        </p>
      </div>

      <div className="space-y-20">
        {services.map((service, index) => {
          const Icon = service.icon
          const isEven = index % 2 === 0

          return (
            <div
              key={service.id}
              id={service.id}
              className={`flex flex-col ${
                isEven ? 'lg:flex-row' : 'lg:flex-row-reverse'
              } items-center gap-12`}
            >
              {/* 内容区域 */}
              <div className="flex-1">
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 bg-gradient-to-br from-accent-500/20 to-primary-500/20 rounded-lg flex items-center justify-center mr-4">
                    <Icon size={24} className="text-accent-400" />
                  </div>
                  <div>
                    <h3 className="text-2xl md:text-3xl font-bold text-white">
                      {service.title}
                    </h3>
                    <p className="text-accent-400 font-medium">
                      {service.subtitle}
                    </p>
                  </div>
                </div>

                <p className="text-gray-300 text-lg leading-relaxed mb-8">
                  {service.description}
                </p>

                {/* 核心优势 */}
                <div className="mb-8">
                  <h4 className="text-lg font-semibold text-white mb-4">核心优势</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {service.benefits.map((benefit) => (
                      <div key={benefit} className="flex items-center">
                        <CheckCircle size={16} className="text-accent-400 mr-3 flex-shrink-0" />
                        <span className="text-gray-300">{benefit}</span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* 技术栈 */}
                <div>
                  <h4 className="text-lg font-semibold text-white mb-4">技术栈</h4>
                  <div className="flex flex-wrap gap-2">
                    {service.technologies.map((tech) => (
                      <span
                        key={tech}
                        className="px-3 py-1 bg-accent-500/10 text-accent-400 text-sm rounded-full border border-accent-500/20"
                      >
                        {tech}
                      </span>
                    ))}
                  </div>
                </div>
              </div>

              {/* 功能列表 */}
              <div className="flex-1">
                <div className="bg-dark-800/50 backdrop-blur-sm border border-dark-700 rounded-xl p-6">
                  <h4 className="text-lg font-semibold text-white mb-6">服务内容</h4>
                  <div className="space-y-3">
                    {service.features.map((feature) => (
                      <div key={feature} className="flex items-center">
                        <div className="w-2 h-2 bg-accent-400 rounded-full mr-4 flex-shrink-0" />
                        <span className="text-gray-300">{feature}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )
        })}
      </div>
    </Section>
  )
}

export default ServiceDetails
