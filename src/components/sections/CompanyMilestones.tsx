import React from 'react'
import { Calendar, Award, Users, Rocket } from 'lucide-react'
import Section from '@/components/ui/Section'

const milestones = [
  {
    year: '2025年7月',
    title: '团队成立',
    description: '两名大学生因为对技术的共同热爱走到一起，决定创建数峪科技',
    icon: Rocket,
    achievements: ['确定发展方针为', '将AI作为主要生产力以及学习工具', '着手于首批产品开发']
  },
  {
    year: '2025年8月',
    title: '技能提升',
    description: '通过在线课程和实践项目，不断提升编程技能和产品思维',
    icon: Users,
    achievements: ['掌握团队Web开发技术', '学习产品设计', '建立开发流程并规范化AI调用']
  },
  {
    year: '2025年9月',
    title: '（进行中）首个产品',
    description: '正在开发并预计于9月31日发布第一个产品 FindU的demo，开始我们的产品之路',
    icon: Award,
    achievements: [
      '打磨完成FindU的MVP开发', 
      '上线FindU的beta测试', 
      '收集用户反馈'
    ]
  },
  {
    year: '2025年10月',
    title: '（未来规划）持续开发',
    description: '继续学习新技术，优化现有产品，着手部署一个团建平台游戏平台MVP',
    icon: Calendar,
    achievements: [
      '继续维护并且改善FindU的功能和使用体验', 
      '部署团建相关应用程序', 
      '通过初期项目运营积累经验，验证市场可行性'
    ]
  }
]

const CompanyMilestones: React.FC = () => {
  return (
    <Section background="gradient" padding="xl">
      <div className="text-center mb-16">
        <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-6">
          我们的成长历程
        </h2>
        <p className="text-xl text-gray-300 max-w-3xl mx-auto">
          刚刚起步，每一步都充满了学习和成长的足迹，
          记录着我们从零开始的启航之路
        </p>
      </div>

      <div className="relative">
        {/* 时间线 */}
        <div className="absolute left-1/2 transform -translate-x-1/2 w-1 bg-gradient-to-b from-accent-500 to-primary-500 h-full hidden lg:block" />

        <div className="space-y-12">
          {milestones.map((milestone, index) => {
            const Icon = milestone.icon
            const isEven = index % 2 === 0

            return (
              <div
                key={milestone.year}
                className={`relative flex items-center ${
                  isEven ? 'lg:flex-row' : 'lg:flex-row-reverse'
                } flex-col lg:space-x-8`}
              >
                {/* 时间线节点 */}
                <div className="absolute left-1/2 transform -translate-x-1/2 w-12 h-12 bg-gradient-to-br from-accent-500 to-primary-500 rounded-full flex items-center justify-center z-10 hidden lg:flex">
                  <Icon size={24} className="text-white" />
                </div>

                {/* 内容卡片 */}
                <div className={`w-full lg:w-5/12 ${isEven ? 'lg:text-right' : 'lg:text-left'} text-center lg:text-left`}>
                  <div className="bg-dark-800/50 backdrop-blur-sm border border-dark-700 rounded-xl p-6 hover:border-accent-500/30 transition-all duration-300">
                    <div className="flex items-center justify-center lg:hidden w-12 h-12 bg-gradient-to-br from-accent-500 to-primary-500 rounded-full mx-auto mb-4">
                      <Icon size={24} className="text-white" />
                    </div>
                    
                    <div className="text-accent-400 text-sm font-medium mb-2">
                      {milestone.year}
                    </div>
                    <h3 className="text-xl font-bold text-white mb-3">
                      {milestone.title}
                    </h3>
                    <p className="text-gray-300 mb-4 leading-relaxed">
                      {milestone.description}
                    </p>
                    
                    {/* 成就列表 */}
                    <div className="space-y-2">
                      {milestone.achievements.map((achievement) => (
                        <div
                          key={achievement}
                          className="flex items-center text-sm text-gray-400"
                        >
                          <div className="w-1.5 h-1.5 bg-accent-400 rounded-full mr-3 flex-shrink-0" />
                          {achievement}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                {/* 占位空间 */}
                <div className="hidden lg:block w-5/12" />
              </div>
            )
          })}
        </div>
      </div>

      {/* 未来规划 */}
      <div className="mt-20 pt-16 border-t border-dark-700 text-center">
        <h3 className="text-2xl md:text-3xl font-bold text-white mb-6">
          未来规划
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
          <div className="text-center">
            <div className="w-16 h-16 bg-gradient-to-br from-accent-500/20 to-primary-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
              <Rocket size={32} className="text-accent-400" />
            </div>
            <h4 className="text-white font-semibold mb-2">技术创新</h4>
            <p className="text-gray-400 text-sm">
              持续投入研发，充分发挥AI等前沿技术的应用，同时不盲目使用新技术，核心仍然是确保产品具有市场且实用可靠
            </p>
          </div>
          <div className="text-center">
            <div className="w-16 h-16 bg-gradient-to-br from-accent-500/20 to-primary-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
              <Users size={32} className="text-accent-400" />
            </div>
            <h4 className="text-white font-semibold mb-2">微型团队</h4>
            <p className="text-gray-400 text-sm">
              保持一个团队维持在小规模开发，避免产生与开发目标不符的非理性扩张
            </p>
          </div>
          <div className="text-center">
            <div className="w-16 h-16 bg-gradient-to-br from-accent-500/20 to-primary-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
              <Award size={32} className="text-accent-400" />
            </div>
            <h4 className="text-white font-semibold mb-2">市场目标</h4>
            <p className="text-gray-400 text-sm">
              维持在力所能及的应用尺度，从寻常的需求点进行切入，开发中小型应用
            </p>
          </div>
        </div>
      </div>
    </Section>
  )
}

export default CompanyMilestones
