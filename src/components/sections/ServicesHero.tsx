import React from 'react'
import { Code2, Smartphone, Settings, Database } from 'lucide-react'
import Section from '@/components/ui/Section'
import Button from '@/components/ui/Button'

const serviceStats = [
  { number: '50+', label: '成功项目' },
  { number: '30+', label: '合作客户' },
  { number: '98%', label: '客户满意度' },
  { number: '24/7', label: '技术支持' }
]

const ServicesHero: React.FC = () => {
  return (
    <Section padding="xl" background="gradient">
      <div className="text-center mb-16">
        <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6">
          专业服务项目
        </h1>
        <p className="text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed mb-8">
          从需求分析到产品交付，我们提供全方位的软件开发服务，
          为您的企业数字化转型提供强有力的技术支持
        </p>
        <Button size="lg" href="/contact">
          立即咨询
        </Button>
      </div>

      {/* 服务统计 */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto mb-16">
        {serviceStats.map((stat) => (
          <div key={stat.label} className="text-center">
            <div className="text-3xl md:text-4xl font-bold gradient-text mb-2">
              {stat.number}
            </div>
            <div className="text-gray-400">{stat.label}</div>
          </div>
        ))}
      </div>

      {/* 核心服务概览 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        <div className="text-center group">
          <div className="w-16 h-16 bg-gradient-to-br from-accent-500/20 to-primary-500/20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
            <Code2 size={32} className="text-accent-400" />
          </div>
          <h3 className="text-lg font-semibold text-white mb-2">定制软件开发</h3>
          <p className="text-gray-400 text-sm">
            企业级软件解决方案
          </p>
        </div>
        <div className="text-center group">
          <div className="w-16 h-16 bg-gradient-to-br from-accent-500/20 to-primary-500/20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
            <Smartphone size={32} className="text-accent-400" />
          </div>
          <h3 className="text-lg font-semibold text-white mb-2">移动应用开发</h3>
          <p className="text-gray-400 text-sm">
            iOS & Android 原生应用
          </p>
        </div>
        <div className="text-center group">
          <div className="w-16 h-16 bg-gradient-to-br from-accent-500/20 to-primary-500/20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
            <Settings size={32} className="text-accent-400" />
          </div>
          <h3 className="text-lg font-semibold text-white mb-2">技术咨询服务</h3>
          <p className="text-gray-400 text-sm">
            架构设计与技术选型
          </p>
        </div>
        <div className="text-center group">
          <div className="w-16 h-16 bg-gradient-to-br from-accent-500/20 to-primary-500/20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
            <Database size={32} className="text-accent-400" />
          </div>
          <h3 className="text-lg font-semibold text-white mb-2">系统集成</h3>
          <p className="text-gray-400 text-sm">
            数据整合与系统对接
          </p>
        </div>
      </div>
    </Section>
  )
}

export default ServicesHero
