import React from 'react'
import Link from 'next/link'
import { Calendar, Clock, User, Tag, ArrowLeft, Share2 } from 'lucide-react'
import Section from '@/components/ui/Section'
import Container from '@/components/ui/Container'
import Button from '@/components/ui/Button'
import { BlogPost as BlogPostType } from '@/lib/blog'
import { formatDate } from '@/lib/utils'

interface BlogPostProps {
  post: BlogPostType
}

const BlogPost: React.FC<BlogPostProps> = ({ post }) => {
  return (
    <>
      {/* 文章头部 */}
      <Section padding="lg" background="gradient">
        <Container size="md">
          <div className="text-center">
            {/* 返回按钮 */}
            <div className="mb-8">
              <Link
                href="/blog"
                className="inline-flex items-center text-accent-400 hover:text-accent-300 transition-colors duration-200"
              >
                <ArrowLeft size={16} className="mr-2" />
                返回博客列表
              </Link>
            </div>

            {/* 分类标签 */}
            <div className="mb-4">
              <span className="px-3 py-1 bg-accent-500/10 text-accent-400 text-sm font-medium rounded-full">
                {post.category}
              </span>
            </div>

            {/* 文章标题 */}
            <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-6 leading-tight">
              {post.title}
            </h1>

            {/* 文章元信息 */}
            <div className="flex flex-wrap justify-center items-center gap-6 text-gray-400 mb-8">
              <div className="flex items-center">
                <User size={16} className="mr-2" />
                <span>{post.author}</span>
              </div>
              <div className="flex items-center">
                <Calendar size={16} className="mr-2" />
                <span>{formatDate(post.date)}</span>
              </div>
              <div className="flex items-center">
                <Clock size={16} className="mr-2" />
                <span>{post.readTime}</span>
              </div>
            </div>

            {/* 标签 */}
            <div className="flex flex-wrap justify-center gap-2 mb-8">
              {post.tags.map((tag) => (
                <span
                  key={tag}
                  className="px-3 py-1 bg-dark-700 text-gray-300 text-sm rounded-full flex items-center"
                >
                  <Tag size={12} className="mr-1" />
                  {tag}
                </span>
              ))}
            </div>

            {/* 分享按钮 */}
            <div className="flex justify-center">
              <Button variant="outline" size="sm">
                <Share2 size={16} className="mr-2" />
                分享文章
              </Button>
            </div>
          </div>
        </Container>
      </Section>

      {/* 文章内容 */}
      <Section padding="xl">
        <Container size="md">
          <article className="prose prose-invert prose-lg max-w-none">
            <div 
              className="blog-content"
              dangerouslySetInnerHTML={{ __html: post.content }}
            />
          </article>

          {/* 文章底部 */}
          <div className="mt-16 pt-8 border-t border-dark-700">
            <div className="flex flex-col md:flex-row justify-between items-center gap-6">
              <div className="text-center md:text-left">
                <h3 className="text-lg font-semibold text-white mb-2">
                  关于作者：{post.author}
                </h3>
                <p className="text-gray-400 text-sm">
                  数峪科技技术专家，专注于软件开发和技术分享
                </p>
              </div>
              <div className="flex gap-4">
                <Button variant="outline" size="sm" href="/blog">
                  更多文章
                </Button>
                <Button size="sm" href="/contact">
                  联系我们
                </Button>
              </div>
            </div>
          </div>
        </Container>
      </Section>

      {/* 相关文章推荐 */}
      <Section background="gradient" padding="lg">
        <Container>
          <div className="text-center mb-12">
            <h2 className="text-2xl md:text-3xl font-bold text-white mb-4">
              相关文章推荐
            </h2>
            <p className="text-gray-300">
              继续探索更多技术内容
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* 这里可以添加相关文章的逻辑 */}
            <div className="bg-dark-800/50 backdrop-blur-sm border border-dark-700 rounded-xl p-6 hover:border-accent-500/30 transition-all duration-300">
              <div className="mb-3">
                <span className="px-2 py-1 bg-accent-500/10 text-accent-400 text-xs rounded-full">
                  前端开发
                </span>
              </div>
              <h3 className="text-lg font-semibold text-white mb-2 hover:text-accent-400 transition-colors">
                <Link href="/blog/nextjs-14-features">
                  Next.js 14 新特性深度解析
                </Link>
              </h3>
              <p className="text-gray-400 text-sm mb-4">
                探索 Next.js 14 带来的革命性变化...
              </p>
              <div className="flex items-center text-gray-500 text-xs">
                <Clock size={12} className="mr-1" />
                8 分钟阅读
              </div>
            </div>

            <div className="bg-dark-800/50 backdrop-blur-sm border border-dark-700 rounded-xl p-6 hover:border-accent-500/30 transition-all duration-300">
              <div className="mb-3">
                <span className="px-2 py-1 bg-accent-500/10 text-accent-400 text-xs rounded-full">
                  系统架构
                </span>
              </div>
              <h3 className="text-lg font-semibold text-white mb-2 hover:text-accent-400 transition-colors">
                <Link href="/blog/microservices-best-practices">
                  微服务架构设计最佳实践
                </Link>
              </h3>
              <p className="text-gray-400 text-sm mb-4">
                从单体应用到微服务的演进过程...
              </p>
              <div className="flex items-center text-gray-500 text-xs">
                <Clock size={12} className="mr-1" />
                12 分钟阅读
              </div>
            </div>

            <div className="bg-dark-800/50 backdrop-blur-sm border border-dark-700 rounded-xl p-6 hover:border-accent-500/30 transition-all duration-300">
              <div className="mb-3">
                <span className="px-2 py-1 bg-accent-500/10 text-accent-400 text-xs rounded-full">
                  人工智能
                </span>
              </div>
              <h3 className="text-lg font-semibold text-white mb-2 hover:text-accent-400 transition-colors">
                <Link href="/blog/ai-code-generation-tools">
                  AI 驱动的代码生成工具对比
                </Link>
              </h3>
              <p className="text-gray-400 text-sm mb-4">
                深入对比各种 AI 编程助手...
              </p>
              <div className="flex items-center text-gray-500 text-xs">
                <Clock size={12} className="mr-1" />
                10 分钟阅读
              </div>
            </div>
          </div>
        </Container>
      </Section>
    </>
  )
}

export default BlogPost
