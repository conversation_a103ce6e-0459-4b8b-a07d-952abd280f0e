import React from 'react'
import { MessageSquare, FileText, Code, TestTube, Rocket, Headphones } from 'lucide-react'
import Section from '@/components/ui/Section'

const processSteps = [
  {
    step: '01',
    icon: MessageSquare,
    title: '需求沟通',
    description: '深入了解您的业务需求和目标，制定初步的解决方案',
    details: ['业务需求分析', '技术可行性评估', '项目范围确定', '初步方案设计']
  },
  {
    step: '02',
    icon: FileText,
    title: '方案设计',
    description: '制定详细的技术方案和项目计划，确保项目顺利进行',
    details: ['系统架构设计', '技术选型确定', '项目计划制定', '资源配置规划']
  },
  {
    step: '03',
    icon: Code,
    title: '开发实施',
    description: '按照敏捷开发流程，高效完成产品开发和功能实现',
    details: ['迭代开发', '代码审查', '进度跟踪', '质量控制']
  },
  {
    step: '04',
    icon: TestTube,
    title: '测试验收',
    description: '全面的测试验证，确保产品质量和用户体验',
    details: ['功能测试', '性能测试', '安全测试', '用户验收']
  },
  {
    step: '05',
    icon: Rocket,
    title: '部署上线',
    description: '协助产品部署上线，确保系统稳定运行',
    details: ['环境部署', '数据迁移', '上线发布', '监控配置']
  },
  {
    step: '06',
    icon: Headphones,
    title: '维护支持',
    description: '提供持续的技术支持和维护服务，保障系统稳定',
    details: ['技术支持', '问题修复', '功能升级', '性能优化']
  }
]

const ServiceProcess: React.FC = () => {
  return (
    <Section background="gradient" padding="xl">
      <div className="text-center mb-16">
        <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-6">
          服务流程
        </h2>
        <p className="text-xl text-gray-300 max-w-3xl mx-auto">
          标准化的服务流程，确保项目高质量交付
        </p>
      </div>

      <div className="relative">
        {/* 连接线 - 桌面端 */}
        <div className="hidden lg:block absolute top-1/2 left-0 right-0 h-0.5 bg-gradient-to-r from-accent-500 to-primary-500 transform -translate-y-1/2" />

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {processSteps.map((step, index) => {
            const Icon = step.icon
            return (
              <div key={step.step} className="relative">
                {/* 步骤卡片 */}
                <div className="bg-dark-800/50 backdrop-blur-sm border border-dark-700 rounded-xl p-6 hover:border-accent-500/30 transition-all duration-300 h-full">
                  {/* 步骤编号 */}
                  <div className="flex items-center justify-center w-12 h-12 bg-gradient-to-br from-accent-500 to-primary-500 rounded-full mx-auto mb-4 relative z-10">
                    <span className="text-white font-bold">{step.step}</span>
                  </div>

                  {/* 图标 */}
                  <div className="flex items-center justify-center w-16 h-16 bg-gradient-to-br from-accent-500/20 to-primary-500/20 rounded-full mx-auto mb-4">
                    <Icon size={32} className="text-accent-400" />
                  </div>

                  {/* 标题和描述 */}
                  <h3 className="text-xl font-bold text-white text-center mb-3">
                    {step.title}
                  </h3>
                  <p className="text-gray-300 text-center mb-6 leading-relaxed">
                    {step.description}
                  </p>

                  {/* 详细内容 */}
                  <div className="space-y-2">
                    {step.details.map((detail) => (
                      <div key={detail} className="flex items-center text-sm text-gray-400">
                        <div className="w-1.5 h-1.5 bg-accent-400 rounded-full mr-3 flex-shrink-0" />
                        {detail}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )
          })}
        </div>
      </div>

      {/* 服务承诺 */}
      <div className="mt-20 pt-16 border-t border-dark-700">
        <div className="text-center mb-12">
          <h3 className="text-2xl md:text-3xl font-bold text-white mb-4">
            服务承诺
          </h3>
          <p className="text-gray-300 max-w-2xl mx-auto">
            我们承诺为每一位客户提供最优质的服务
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          <div className="text-center">
            <div className="text-3xl font-bold gradient-text mb-2">100%</div>
            <div className="text-white font-semibold mb-2">按时交付</div>
            <div className="text-gray-400 text-sm">严格按照项目计划执行</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold gradient-text mb-2">24/7</div>
            <div className="text-white font-semibold mb-2">技术支持</div>
            <div className="text-gray-400 text-sm">全天候技术支持服务</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold gradient-text mb-2">1年</div>
            <div className="text-white font-semibold mb-2">免费维护</div>
            <div className="text-gray-400 text-sm">项目交付后免费维护</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold gradient-text mb-2">98%</div>
            <div className="text-white font-semibold mb-2">客户满意度</div>
            <div className="text-gray-400 text-sm">客户满意是我们的目标</div>
          </div>
        </div>
      </div>
    </Section>
  )
}

export default ServiceProcess
