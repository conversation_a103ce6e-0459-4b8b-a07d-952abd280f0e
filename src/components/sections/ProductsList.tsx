import React from 'react'
import Link from 'next/link'
import { ExternalLink, Globe, Users, Calendar, Tag } from 'lucide-react'
import Section from '@/components/ui/Section'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import Button from '@/components/ui/Button'

const products = [
  {
    id: 'findu',
    name: 'FindU',
    type: 'Web应用',
    description: '一个简洁实用的在线工具，帮助用户快速找到所需的信息和资源。',
    features: [
      '简洁直观的用户界面',
      '快速搜索和筛选功能',
      '响应式设计，支持多设备访问',
      '免费使用，无需注册'
    ],
    status: '正在开发',
    url: 'https://findu.shuyutech.online',
    category: 'web',
    tags: ['工具', '搜索', '免费'],
    launchDate: '2024年',
    image: '/images/products/findu-preview.jpg' // 占位符
  }
]

const ProductsList: React.FC = () => {
  return (
    <Section padding="xl">
      <div className="text-center mb-16">
        <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-6">
          产品列表
        </h2>
        <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-8">
          探索我们正在开发和已经发布的产品
        </p>
      </div>

      {/* 产品展示 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-6xl mx-auto">
        {products.map((product) => (
          <Card key={product.id} className="group h-full">
            <CardHeader>
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-2">
                  <span className={`px-3 py-1 text-xs font-medium rounded-full ${
                    product.category === 'web' 
                      ? 'bg-blue-500/10 text-blue-400' 
                      : 'bg-green-500/10 text-green-400'
                  }`}>
                    {product.type}
                  </span>
                  <span className={`px-3 py-1 text-xs font-medium rounded-full ${
                    product.status === '已发布' 
                      ? 'bg-green-500/10 text-green-400'
                      : 'bg-yellow-500/10 text-yellow-400'
                  }`}>
                    {product.status}
                  </span>
                </div>
                <div className="flex items-center text-gray-400 text-sm">
                  <Calendar size={14} className="mr-1" />
                  {product.launchDate}
                </div>
              </div>
              
              <CardTitle className="text-2xl group-hover:text-accent-400 transition-colors duration-200">
                {product.name}
              </CardTitle>
              
              <CardDescription className="text-base leading-relaxed">
                {product.description}
              </CardDescription>
            </CardHeader>
            
            <CardContent>
              {/* 产品特性 */}
              <div className="mb-6">
                <h4 className="text-white font-semibold mb-3">主要特性</h4>
                <ul className="space-y-2">
                  {product.features.map((feature, index) => (
                    <li key={index} className="flex items-start text-gray-300 text-sm">
                      <div className="w-1.5 h-1.5 bg-accent-400 rounded-full mr-3 mt-2 flex-shrink-0" />
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>

              {/* 标签 */}
              <div className="flex flex-wrap gap-2 mb-6">
                {product.tags.map((tag) => (
                  <span
                    key={tag}
                    className="px-2 py-1 bg-dark-700 text-gray-300 text-xs rounded flex items-center"
                  >
                    <Tag size={10} className="mr-1" />
                    {tag}
                  </span>
                ))}
              </div>

              {/* 访问按钮 */}
              <div className="flex gap-4">
                {product.category === 'web' ? (
                  <Button
                    href={product.url}
                    external={true}
                    className="flex-1"
                  >
                    <Globe size={16} className="mr-2" />
                    访问应用
                    <ExternalLink size={14} className="ml-2" />
                  </Button>
                ) : (
                  <>
                    <Button
                      href={product.url}
                      external={true}
                      className="flex-1"
                    >
                      下载应用
                    </Button>
                  </>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* 开发中提示 */}
      <div className="mt-16 bg-dark-800/50 backdrop-blur-sm border border-dark-700 rounded-xl p-8 text-center max-w-4xl mx-auto">
        <h3 className="text-2xl font-bold text-white mb-4">
          更多产品正在开发中
        </h3>
        <p className="text-gray-300 mb-6 max-w-2xl mx-auto">
          我们是一个年轻的团队，正在努力开发更多实用的产品。
          如果您有好的想法或建议，欢迎与我们联系！
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button variant="outline" href="/contact">
            联系我们
          </Button>
          <Button variant="outline" href="/about">
            了解团队
          </Button>
        </div>
      </div>

      {/* 产品统计 */}
      <div className="mt-16 pt-16 border-t border-dark-700">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center max-w-4xl mx-auto">
          <div>
            <div className="text-3xl md:text-4xl font-bold gradient-text mb-2">1</div>
            <div className="text-gray-400">当前产品</div>
          </div>
          <div>
            <div className="text-3xl md:text-4xl font-bold gradient-text mb-2">Web</div>
            <div className="text-gray-400">应用类型</div>
          </div>
          <div>
            <div className="text-3xl md:text-4xl font-bold gradient-text mb-2">免费</div>
            <div className="text-gray-400">使用方式</div>
          </div>
          <div>
            <div className="text-3xl md:text-4xl font-bold gradient-text mb-2">2024</div>
            <div className="text-gray-400">开始年份</div>
          </div>
        </div>
      </div>
    </Section>
  )
}

export default ProductsList
