import React from 'react'
import { TrendingUp, Clock, Award, Heart } from 'lucide-react'
import Section from '@/components/ui/Section'

const stats = [
  {
    icon: TrendingUp,
    title: '业务增长',
    description: '我们的项目平均为客户带来',
    value: '40%',
    suffix: '业务增长'
  },
  {
    icon: Clock,
    title: '交付效率',
    description: '项目按时交付率达到',
    value: '100%',
    suffix: '准时交付'
  },
  {
    icon: Award,
    title: '质量保证',
    description: '客户满意度持续保持',
    value: '98%',
    suffix: '满意度'
  },
  {
    icon: Heart,
    title: '长期合作',
    description: '客户续约合作率达到',
    value: '85%',
    suffix: '续约率'
  }
]

const testimonials = [
  {
    name: '张总',
    company: '某大型零售企业',
    role: 'CTO',
    content: '数峪科技团队的专业能力和服务质量都非常出色。他们不仅按时交付了高质量的产品，还在后续的维护和升级中提供了优质的技术支持。',
    project: '智慧零售管理系统'
  },
  {
    name: '李总',
    company: '某知名教育机构',
    role: '技术总监',
    content: '合作过程中，数峪科技展现了深厚的技术功底和丰富的项目经验。他们能够准确理解我们的需求，并提供了超出预期的解决方案。',
    project: '在线教育平台'
  },
  {
    name: '王总',
    company: '某物流科技公司',
    role: '产品经理',
    content: '项目从需求分析到最终交付，整个过程都非常顺利。数峪科技的团队沟通效率很高，能够快速响应我们的需求变更。',
    project: '智能物流配送App'
  }
]

const PortfolioStats: React.FC = () => {
  return (
    <Section background="gradient" padding="xl">
      <div className="text-center mb-16">
        <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-6">
          项目成果
        </h2>
        <p className="text-xl text-gray-300 max-w-3xl mx-auto">
          数据说话，用实际成果证明我们的专业能力
        </p>
      </div>

      {/* 统计数据 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-20">
        {stats.map((stat) => {
          const Icon = stat.icon
          return (
            <div key={stat.title} className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-accent-500/20 to-primary-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <Icon size={32} className="text-accent-400" />
              </div>
              <div className="text-3xl md:text-4xl font-bold gradient-text mb-2">
                {stat.value}
              </div>
              <div className="text-white font-semibold mb-2">
                {stat.suffix}
              </div>
              <div className="text-gray-400 text-sm">
                {stat.description}
              </div>
            </div>
          )
        })}
      </div>

      {/* 客户评价 */}
      <div>
        <div className="text-center mb-12">
          <h3 className="text-2xl md:text-3xl font-bold text-white mb-4">
            客户评价
          </h3>
          <p className="text-gray-300 max-w-2xl mx-auto">
            听听我们的客户怎么说
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <div
              key={index}
              className="bg-dark-800/50 backdrop-blur-sm border border-dark-700 rounded-xl p-6 hover:border-accent-500/30 transition-all duration-300"
            >
              <div className="mb-4">
                <div className="text-accent-400 text-2xl mb-2">"</div>
                <p className="text-gray-300 leading-relaxed italic">
                  {testimonial.content}
                </p>
              </div>
              
              <div className="border-t border-dark-700 pt-4">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-white font-semibold">
                      {testimonial.name}
                    </div>
                    <div className="text-gray-400 text-sm">
                      {testimonial.role}
                    </div>
                    <div className="text-gray-500 text-xs">
                      {testimonial.company}
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-accent-400 text-xs">
                      项目：{testimonial.project}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* 合作流程简述 */}
      <div className="mt-20 pt-16 border-t border-dark-700">
        <div className="text-center mb-12">
          <h3 className="text-2xl md:text-3xl font-bold text-white mb-4">
            为什么选择我们？
          </h3>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          <div className="text-center">
            <div className="w-12 h-12 bg-accent-500/10 rounded-lg flex items-center justify-center mx-auto mb-4">
              <span className="text-accent-400 font-bold text-lg">1</span>
            </div>
            <h4 className="text-white font-semibold mb-2">深度理解需求</h4>
            <p className="text-gray-400 text-sm">
              我们花时间深入了解您的业务，确保解决方案完全符合需求
            </p>
          </div>
          <div className="text-center">
            <div className="w-12 h-12 bg-accent-500/10 rounded-lg flex items-center justify-center mx-auto mb-4">
              <span className="text-accent-400 font-bold text-lg">2</span>
            </div>
            <h4 className="text-white font-semibold mb-2">专业技术团队</h4>
            <p className="text-gray-400 text-sm">
              经验丰富的技术专家，掌握最新的技术栈和最佳实践
            </p>
          </div>
          <div className="text-center">
            <div className="w-12 h-12 bg-accent-500/10 rounded-lg flex items-center justify-center mx-auto mb-4">
              <span className="text-accent-400 font-bold text-lg">3</span>
            </div>
            <h4 className="text-white font-semibold mb-2">敏捷开发流程</h4>
            <p className="text-gray-400 text-sm">
              采用敏捷开发方法，快速迭代，及时响应需求变化
            </p>
          </div>
          <div className="text-center">
            <div className="w-12 h-12 bg-accent-500/10 rounded-lg flex items-center justify-center mx-auto mb-4">
              <span className="text-accent-400 font-bold text-lg">4</span>
            </div>
            <h4 className="text-white font-semibold mb-2">持续技术支持</h4>
            <p className="text-gray-400 text-sm">
              项目交付后提供持续的技术支持和维护服务
            </p>
          </div>
        </div>
      </div>
    </Section>
  )
}

export default PortfolioStats
