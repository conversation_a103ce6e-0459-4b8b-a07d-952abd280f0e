import React from 'react'
import { Award, Users, Code, Zap } from 'lucide-react'
import Section from '@/components/ui/Section'
import Button from '@/components/ui/Button'

const achievements = [
  {
    icon: Award,
    number: '50+',
    label: '成功项目',
    description: '涵盖多个行业领域'
  },
  {
    icon: Users,
    number: '30+',
    label: '合作客户',
    description: '从初创到大型企业'
  },
  {
    icon: Code,
    number: '100万+',
    label: '代码行数',
    description: '高质量代码交付'
  },
  {
    icon: Zap,
    number: '98%',
    label: '客户满意度',
    description: '持续优质服务'
  }
]

const PortfolioHero: React.FC = () => {
  return (
    <Section padding="xl" background="gradient">
      <div className="text-center mb-16">
        <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6">
          成功案例展示
        </h1>
        <p className="text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed mb-8">
          我们为不同行业的客户提供了优质的软件开发服务，
          从企业管理系统到移动应用，从电商平台到数据分析工具，
          每一个项目都体现了我们的专业能力和技术实力。
        </p>
        <Button size="lg" href="/contact">
          开始您的项目
        </Button>
      </div>

      {/* 成就统计 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        {achievements.map((achievement) => {
          const Icon = achievement.icon
          return (
            <div key={achievement.label} className="text-center group">
              <div className="w-16 h-16 bg-gradient-to-br from-accent-500/20 to-primary-500/20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                <Icon size={32} className="text-accent-400" />
              </div>
              <div className="text-3xl md:text-4xl font-bold gradient-text mb-2">
                {achievement.number}
              </div>
              <div className="text-white font-semibold mb-2">
                {achievement.label}
              </div>
              <div className="text-gray-400 text-sm">
                {achievement.description}
              </div>
            </div>
          )
        })}
      </div>

      {/* 行业覆盖 */}
      <div className="mt-20 pt-16 border-t border-dark-700">
        <div className="text-center mb-12">
          <h2 className="text-2xl md:text-3xl font-bold text-white mb-4">
            服务行业
          </h2>
          <p className="text-gray-300 max-w-2xl mx-auto">
            我们的项目覆盖多个行业领域，积累了丰富的行业经验
          </p>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-6">
          {[
            '电商零售',
            '金融科技',
            '教育培训',
            '医疗健康',
            '制造业',
            '物流运输',
            '房地产',
            '餐饮服务',
            '文化娱乐',
            '政府机构',
            '非营利组织',
            '初创企业'
          ].map((industry) => (
            <div
              key={industry}
              className="text-center p-4 bg-dark-800/30 rounded-lg hover:bg-dark-700/50 transition-colors duration-200"
            >
              <span className="text-gray-300 text-sm">{industry}</span>
            </div>
          ))}
        </div>
      </div>
    </Section>
  )
}

export default PortfolioHero
