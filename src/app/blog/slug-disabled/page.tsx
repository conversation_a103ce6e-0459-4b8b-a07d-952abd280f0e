import React from 'react'
import { Metada<PERSON> } from 'next'
import { notFound } from 'next/navigation'
import BlogPost from '@/components/sections/BlogPost'
import { getPostData, getSortedPostsData } from '@/lib/blog'

interface BlogPostPageProps {
  params: {
    slug: string
  }
}

export function generateStaticParams() {
  const posts = getSortedPostsData()
  return posts.map((post) => ({
    slug: post.slug,
  }))
}

export async function generateMetadata({ params }: BlogPostPageProps): Promise<Metadata> {
  const post = await getPostData(params.slug)
  
  if (!post) {
    return {
      title: '文章未找到 | 数峪科技',
    }
  }

  return {
    title: `${post.title} | 数峪科技技术博客`,
    description: post.excerpt,
    keywords: post.tags.join(', '),
    authors: [{ name: post.author }],
    openGraph: {
      title: post.title,
      description: post.excerpt,
      type: 'article',
      publishedTime: post.date,
      authors: [post.author],
      tags: post.tags,
    },
  }
}

export default async function BlogPostPage({ params }: BlogPostPageProps) {
  const post = await getPostData(params.slug)

  if (!post) {
    notFound()
  }

  return (
    <div className="pt-20">
      <BlogPost post={post} />
    </div>
  )
}
