import React from 'react'
import { Metadata } from 'next'
import BlogHero from '@/components/sections/BlogHero'
import BlogList from '@/components/sections/BlogList'
import { getSortedPostsData } from '@/lib/blog'

export const metadata: Metadata = {
  title: '技术博客 | 数峪科技 - 分享技术洞察与开发经验',
  description: '数峪科技技术博客，分享最新的技术趋势、开发经验和行业洞察。涵盖前端开发、后端架构、移动应用、人工智能等多个技术领域。',
  keywords: '技术博客,前端开发,后端架构,移动应用,人工智能,软件开发,技术分享',
}

export default function BlogPage() {
  const posts = getSortedPostsData()

  return (
    <div className="pt-20">
      <BlogHero />
      <BlogList posts={posts} />
    </div>
  )
}
