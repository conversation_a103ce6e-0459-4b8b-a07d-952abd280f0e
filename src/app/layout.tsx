import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import GlobalBackground from '@/components/layout/GlobalBackground'
import ResponsiveIndicator from '@/components/ui/ResponsiveIndicator'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: '数峪科技 | ShuyuTech - 以卓越代码，构建未来软件',
  description: '数峪科技是一家专注于定制软件开发、移动应用开发和技术咨询的创新型科技公司。我们以精英团队、敏捷开发和客户至上的理念，为企业提供高质量的技术解决方案。',
  keywords: '数峪科技,ShuyuTech,软件开发,移动应用,技术咨询,定制开发,企业软件',
  authors: [{ name: 'ShuyuTech' }],
  creator: 'ShuyuTech',
  publisher: 'ShuyuTech',
  openGraph: {
    type: 'website',
    locale: 'zh_CN',
    url: 'https://shuyutech.com',
    title: '数峪科技 | ShuyuTech - 以卓越代码，构建未来软件',
    description: '数峪科技是一家专注于定制软件开发、移动应用开发和技术咨询的创新型科技公司。',
    siteName: '数峪科技官网',
  },
  twitter: {
    card: 'summary_large_image',
    title: '数峪科技 | ShuyuTech',
    description: '以卓越代码，构建未来软件',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="zh-CN">
      <body className={inter.className}>
        <GlobalBackground />
        <div className="min-h-screen flex flex-col relative">
          <Header />
          <main className="flex-grow">
            {children}
          </main>
          <Footer />
          <ResponsiveIndicator />
        </div>
      </body>
    </html>
  )
}
