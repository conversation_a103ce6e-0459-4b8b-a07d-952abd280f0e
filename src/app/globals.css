@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap');

@layer base {
  html {
    scroll-behavior: smooth;
  }
  
  body {
    @apply bg-gradient-to-br from-blue-50 to-cyan-50 text-gray-800 font-sans antialiased;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold text-gray-800;
  }
  
  h1 {
    @apply text-4xl md:text-5xl lg:text-6xl;
  }
  
  h2 {
    @apply text-3xl md:text-4xl lg:text-5xl;
  }
  
  h3 {
    @apply text-2xl md:text-3xl;
  }
  
  h4 {
    @apply text-xl md:text-2xl;
  }
  
  p {
    @apply text-gray-600 leading-relaxed;
  }

  a {
    @apply text-primary-600 hover:text-primary-700 transition-colors duration-200;
  }
}

@layer components {
  .btn-primary {
    @apply bg-primary-500 hover:bg-primary-600 text-white font-medium px-6 py-3 rounded-lg transition-all duration-200 hover:shadow-lg hover:shadow-primary-500/25;
  }

  .btn-secondary {
    @apply bg-transparent border border-primary-500 text-primary-600 hover:bg-primary-500 hover:text-white font-medium px-6 py-3 rounded-lg transition-all duration-200;
  }

  .card {
    @apply bg-white/20 backdrop-blur-sm border border-white/30 rounded-xl p-6 hover:border-primary-500/30 transition-all duration-300;
  }
  
  .section-padding {
    @apply py-16 md:py-24 lg:py-32;
  }
  
  .container-custom {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }
  
  .gradient-text {
    @apply bg-gradient-to-r from-primary-500 via-accent-500 to-secondary-500 bg-clip-text text-transparent;
  }

  .hero-gradient {
    background: radial-gradient(ellipse at center, rgba(14, 165, 233, 0.1) 0%, transparent 70%);
  }
}

@layer utilities {
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }

  .backdrop-blur-custom {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
  }

  /* 新增磨砂玻璃效果工具类 */
  .glass-effect {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .glass-hover {
    transition: all 0.3s ease;
  }

  .glass-hover:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  }
}

/* 动画关键帧 */
@keyframes slide-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* 动画工具类 */
.animate-slide-up {
  animation: slide-up 0.6s ease-out forwards;
}

.animate-fade-in {
  animation: fade-in 0.6s ease-out forwards;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

/* 代码块样式 */
pre {
  @apply bg-dark-900 border border-dark-700 rounded-lg p-4 overflow-x-auto;
}

code {
  @apply bg-dark-800 text-accent-300 px-2 py-1 rounded text-sm font-mono;
}

pre code {
  @apply bg-transparent p-0;
}

/* 引用样式 */
blockquote {
  @apply border-l-4 border-accent-500 pl-4 italic text-gray-400 my-4;
}

/* 列表样式 */
ul, ol {
  @apply space-y-2 text-gray-300;
}

ul li {
  @apply relative pl-6;
}

ul li::before {
  content: "•";
  @apply absolute left-0 text-accent-400;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-dark-900;
}

::-webkit-scrollbar-thumb {
  @apply bg-dark-600 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-dark-500;
}

/* 博客内容样式 */
.blog-content {
  @apply text-gray-300 leading-relaxed;
}

.blog-content h1,
.blog-content h2,
.blog-content h3,
.blog-content h4,
.blog-content h5,
.blog-content h6 {
  @apply text-white font-semibold mt-8 mb-4;
}

.blog-content h1 {
  @apply text-3xl;
}

.blog-content h2 {
  @apply text-2xl;
}

.blog-content h3 {
  @apply text-xl;
}

.blog-content p {
  @apply mb-6 leading-relaxed;
}

.blog-content ul,
.blog-content ol {
  @apply mb-6 pl-6;
}

.blog-content li {
  @apply mb-2;
}

.blog-content a {
  @apply text-accent-400 hover:text-accent-300 underline;
}

.blog-content strong {
  @apply text-white font-semibold;
}

.blog-content em {
  @apply italic;
}

.blog-content img {
  @apply rounded-lg my-8 w-full;
}

.blog-content table {
  @apply w-full border-collapse border border-dark-700 my-8;
}

.blog-content th,
.blog-content td {
  @apply border border-dark-700 px-4 py-2;
}

.blog-content th {
  @apply bg-dark-800 text-white font-semibold;
}

.blog-content td {
  @apply bg-dark-900;
}
