import fs from 'fs'
import path from 'path'
import matter from 'gray-matter'
import { remark } from 'remark'
import html from 'remark-html'

const postsDirectory = path.join(process.cwd(), 'src/content/blog')

export interface BlogPost {
  slug: string
  title: string
  date: string
  excerpt: string
  content: string
  author: string
  category: string
  tags: string[]
  readTime: string
  featured?: boolean
}

export interface BlogPostMeta {
  slug: string
  title: string
  date: string
  excerpt: string
  author: string
  category: string
  tags: string[]
  readTime: string
  featured?: boolean
}

export function getSortedPostsData(): BlogPostMeta[] {
  // 如果目录不存在，返回模拟数据
  if (!fs.existsSync(postsDirectory)) {
    return getMockPosts()
  }

  const fileNames = fs.readdirSync(postsDirectory)
  const allPostsData = fileNames
    .filter(fileName => fileName.endsWith('.md'))
    .map((fileName) => {
      const slug = fileName.replace(/\.md$/, '')
      const fullPath = path.join(postsDirectory, fileName)
      const fileContents = fs.readFileSync(fullPath, 'utf8')
      const matterResult = matter(fileContents)

      return {
        slug,
        title: matterResult.data.title,
        date: matterResult.data.date,
        excerpt: matterResult.data.excerpt,
        author: matterResult.data.author,
        category: matterResult.data.category,
        tags: matterResult.data.tags || [],
        readTime: matterResult.data.readTime,
        featured: matterResult.data.featured || false,
      }
    })

  return allPostsData.sort((a, b) => {
    if (a.date < b.date) {
      return 1
    } else {
      return -1
    }
  })
}

export async function getPostData(slug: string): Promise<BlogPost | null> {
  // 如果目录不存在，返回模拟数据
  if (!fs.existsSync(postsDirectory)) {
    return getMockPostData(slug)
  }

  try {
    const fullPath = path.join(postsDirectory, `${slug}.md`)
    const fileContents = fs.readFileSync(fullPath, 'utf8')
    const matterResult = matter(fileContents)
    const processedContent = await remark()
      .use(html)
      .process(matterResult.content)
    const contentHtml = processedContent.toString()

    return {
      slug,
      content: contentHtml,
      title: matterResult.data.title,
      date: matterResult.data.date,
      excerpt: matterResult.data.excerpt,
      author: matterResult.data.author,
      category: matterResult.data.category,
      tags: matterResult.data.tags || [],
      readTime: matterResult.data.readTime,
      featured: matterResult.data.featured || false,
    }
  } catch (error) {
    return null
  }
}

export function getAllPostSlugs() {
  if (!fs.existsSync(postsDirectory)) {
    return getMockPosts().map(post => post.slug)
  }

  const fileNames = fs.readdirSync(postsDirectory)
  return fileNames
    .filter(fileName => fileName.endsWith('.md'))
    .map((fileName) => fileName.replace(/\.md$/, ''))
}

// 模拟数据
function getMockPosts(): BlogPostMeta[] {
  return [
    {
      slug: 'nextjs-14-features',
      title: 'Next.js 14 新特性深度解析',
      date: '2024-01-15',
      excerpt: '探索 Next.js 14 带来的革命性变化，包括 App Router、Server Components 和性能优化等关键特性。',
      author: '张伟',
      category: '前端开发',
      tags: ['Next.js', 'React', '前端框架'],
      readTime: '8 分钟',
      featured: true
    },
    {
      slug: 'microservices-best-practices',
      title: '微服务架构设计最佳实践',
      date: '2024-01-10',
      excerpt: '从单体应用到微服务的演进过程，以及在实际项目中如何设计和实现高可用的微服务架构。',
      author: '李明',
      category: '系统架构',
      tags: ['微服务', '架构设计', '分布式系统'],
      readTime: '12 分钟',
      featured: true
    },
    {
      slug: 'ai-code-generation-tools',
      title: 'AI 驱动的代码生成工具对比',
      date: '2024-01-05',
      excerpt: '深入对比 GitHub Copilot、ChatGPT 和其他 AI 编程助手，分析它们在实际开发中的应用场景。',
      author: '王芳',
      category: '人工智能',
      tags: ['AI', '代码生成', '开发工具'],
      readTime: '10 分钟',
      featured: false
    }
  ]
}

function getMockPostData(slug: string): BlogPost | null {
  const mockPosts = getMockPosts()
  const post = mockPosts.find(p => p.slug === slug)
  
  if (!post) return null

  return {
    ...post,
    content: `<h2>这是一篇示例文章</h2>
    <p>这里是文章的正文内容。在实际项目中，这些内容将从 Markdown 文件中读取并转换为 HTML。</p>
    <h3>主要特性</h3>
    <ul>
      <li>支持 Markdown 语法</li>
      <li>代码高亮显示</li>
      <li>响应式设计</li>
      <li>SEO 优化</li>
    </ul>
    <p>更多内容请查看完整的博客系统实现。</p>`
  }
}
