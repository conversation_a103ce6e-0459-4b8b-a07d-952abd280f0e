import { Metadata } from 'next'

interface SEOProps {
  title?: string
  description?: string
  keywords?: string
  image?: string
  url?: string
  type?: 'website' | 'article'
  publishedTime?: string
  authors?: string[]
  tags?: string[]
}

const defaultSEO = {
  title: '数峪科技 | ShuyuTech - 以卓越代码，构建未来软件',
  description: '数峪科技是一家专注于定制软件开发、移动应用开发和技术咨询的创新型科技公司。我们以精英团队、敏捷开发和客户至上的理念，为企业提供高质量的技术解决方案。',
  keywords: '数峪科技,ShuyuTech,软件开发,移动应用,技术咨询,定制开发,企业软件',
  image: '/images/og-image.jpg',
  url: 'https://shuyutech.com',
  type: 'website' as const
}

export function generateSEO({
  title,
  description,
  keywords,
  image,
  url,
  type = 'website',
  publishedTime,
  authors,
  tags
}: SEOProps = {}): Metadata {
  const seoTitle = title ? `${title} | 数峪科技` : defaultSEO.title
  const seoDescription = description || defaultSEO.description
  const seoKeywords = keywords || defaultSEO.keywords
  const seoImage = image || defaultSEO.image
  const seoUrl = url || defaultSEO.url

  const metadata: Metadata = {
    title: seoTitle,
    description: seoDescription,
    keywords: seoKeywords,
    authors: authors ? authors.map(name => ({ name })) : [{ name: 'ShuyuTech' }],
    creator: 'ShuyuTech',
    publisher: 'ShuyuTech',
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    openGraph: {
      type,
      locale: 'zh_CN',
      url: seoUrl,
      title: seoTitle,
      description: seoDescription,
      siteName: '数峪科技官网',
      images: [
        {
          url: seoImage,
          width: 1200,
          height: 630,
          alt: seoTitle,
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: seoTitle,
      description: seoDescription,
      images: [seoImage],
    },
    alternates: {
      canonical: seoUrl,
    },
  }

  // 添加文章特定的元数据
  if (type === 'article' && publishedTime) {
    metadata.openGraph = {
      ...metadata.openGraph,
      type: 'article',
      publishedTime,
      authors: authors,
      tags,
    }
  }

  return metadata
}

// 结构化数据
export function generateOrganizationSchema() {
  return {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: '数峪科技',
    alternateName: 'ShuyuTech',
    url: 'https://shuyutech.com',
    logo: 'https://shuyutech.com/images/logo.png',
    description: '专注于定制软件开发、移动应用开发和技术咨询的创新型科技公司',
    address: {
      '@type': 'PostalAddress',
      streetAddress: '创新大厦 A 座 1001 室',
      addressLocality: '朝阳区',
      addressRegion: '北京市',
      postalCode: '100000',
      addressCountry: 'CN'
    },
    contactPoint: {
      '@type': 'ContactPoint',
      telephone: '+86-************',
      contactType: 'customer service',
      availableLanguage: 'Chinese'
    },
    sameAs: [
      'https://github.com/shuyutech',
      'https://linkedin.com/company/shuyutech'
    ]
  }
}

export function generateWebsiteSchema() {
  return {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name: '数峪科技官网',
    url: 'https://shuyutech.com',
    description: '数峪科技官方网站，提供软件开发、移动应用和技术咨询服务',
    publisher: {
      '@type': 'Organization',
      name: '数峪科技'
    },
    potentialAction: {
      '@type': 'SearchAction',
      target: 'https://shuyutech.com/search?q={search_term_string}',
      'query-input': 'required name=search_term_string'
    }
  }
}
