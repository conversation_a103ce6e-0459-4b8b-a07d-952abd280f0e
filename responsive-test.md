# 响应式设计测试清单

## 测试设备尺寸

### 移动端 (320px - 768px)
- iPhone SE (375px)
- iPhone 12 (390px)
- Samsung Galaxy S20 (360px)

### 平板端 (768px - 1024px)
- iPad (768px)
- iPad Pro (1024px)

### 桌面端 (1024px+)
- 小屏桌面 (1024px)
- 中屏桌面 (1440px)
- 大屏桌面 (1920px)

## 测试页面

### 1. 首页 (/)
- [ ] Hero 区在移动端正确显示
- [ ] 服务卡片在不同屏幕下正确排列
- [ ] 统计数据在移动端垂直排列
- [ ] 导航菜单在移动端可正常使用

### 2. 关于我们 (/about)
- [ ] 公司愿景卡片响应式布局
- [ ] 团队介绍在移动端正确显示
- [ ] 发展历程时间线在移动端适配

### 3. 服务项目 (/services)
- [ ] 服务详情左右布局在移动端变为上下布局
- [ ] 价格卡片在移动端正确排列
- [ ] 流程步骤在移动端垂直显示

### 4. 产品案例 (/portfolio)
- [ ] 案例网格在不同屏幕下正确调整
- [ ] 筛选按钮在移动端正确换行
- [ ] 案例卡片内容在移动端完整显示

### 5. 技术博客 (/blog)
- [ ] 博客列表在移动端正确排列
- [ ] 搜索和筛选在移动端正确布局
- [ ] 文章卡片在移动端完整显示

### 6. 博客详情 (/blog/[slug])
- [ ] 文章内容在移动端可读性良好
- [ ] 代码块在移动端正确显示
- [ ] 相关文章推荐在移动端正确排列

### 7. 联系我们 (/contact)
- [ ] 联系表单在移动端易于填写
- [ ] 联系信息在移动端正确显示
- [ ] 表单和信息在桌面端左右布局

## 通用检查项

### 导航
- [ ] 移动端汉堡菜单正常工作
- [ ] 桌面端导航正确显示
- [ ] Logo 在所有设备上正确显示

### 字体和间距
- [ ] 标题在移动端大小合适
- [ ] 正文在移动端可读性良好
- [ ] 间距在不同设备上协调

### 交互元素
- [ ] 按钮在移动端易于点击
- [ ] 链接在移动端正确响应
- [ ] 表单元素在移动端易于操作

### 性能
- [ ] 页面在移动端加载速度合理
- [ ] 图片在不同设备上正确缩放
- [ ] 动画在移动端流畅运行

## 测试工具

1. 浏览器开发者工具
   - Chrome DevTools 设备模拟
   - Firefox 响应式设计模式

2. 在线测试工具
   - Responsive Design Checker
   - BrowserStack

3. 实际设备测试
   - 真实手机和平板测试
   - 不同操作系统测试

## 修复优先级

### 高优先级
- 导航功能问题
- 内容无法正常显示
- 表单无法正常使用

### 中优先级
- 布局轻微错位
- 间距不够协调
- 字体大小需要调整

### 低优先级
- 细微的视觉优化
- 动画效果调整
- 性能微调
